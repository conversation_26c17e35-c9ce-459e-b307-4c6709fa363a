# NocoBase Collection 默认字段配置

## 概述

当创建 NocoBase collection 时，系统会根据配置自动添加以下默认字段。**重要提示**：这些默认字段需要在创建 collection 时显式添加到 `fields` 数组中，否则它们不会在 Collection API 的字段列表中显示，但在数据库层面仍然存在。

## 默认字段列表

### 1. ID 字段 (id)
- **类型**: `bigInt`
- **属性**: 
  - `autoIncrement: true` - 自动递增
  - `primaryKey: true` - 主键
  - `allowNull: false` - 不允许为空
- **Interface**: `id`
- **UI Schema**:
  ```json
  {
    "type": "number",
    "title": "{{t(\"ID\")}}",
    "x-component": "InputNumber",
    "x-read-pretty": true
  }
  ```

### 2. 创建时间 (createdAt)
- **类型**: `date`
- **Interface**: `createdAt`
- **属性**: `field: "createdAt"`
- **UI Schema**:
  ```json
  {
    "type": "datetime",
    "title": "{{t(\"Created at\")}}",
    "x-component": "DatePicker",
    "x-component-props": {},
    "x-read-pretty": true
  }
  ```

### 3. 更新时间 (updatedAt)
- **类型**: `date`
- **Interface**: `updatedAt`
- **属性**: `field: "updatedAt"`
- **UI Schema**:
  ```json
  {
    "type": "datetime",
    "title": "{{t(\"Last updated at\")}}",
    "x-component": "DatePicker",
    "x-component-props": {},
    "x-read-pretty": true
  }
  ```

### 4. 创建人 (createdBy)
- **类型**: `belongsTo`
- **Interface**: `createdBy`
- **关联配置**:
  - `target: "users"` - 关联到用户表
  - `foreignKey: "createdById"` - 外键字段
- **UI Schema**:
  ```json
  {
    "type": "object",
    "title": "{{t(\"Created by\")}}",
    "x-component": "AssociationField",
    "x-component-props": {
      "fieldNames": {
        "value": "id",
        "label": "nickname"
      }
    },
    "x-read-pretty": true
  }
  ```

### 5. 更新人 (updatedBy)
- **类型**: `belongsTo`
- **Interface**: `updatedBy`
- **关联配置**:
  - `target: "users"` - 关联到用户表
  - `foreignKey: "updatedById"` - 外键字段
- **UI Schema**:
  ```json
  {
    "type": "object",
    "title": "{{t(\"Last updated by\")}}",
    "x-component": "AssociationField",
    "x-component-props": {
      "fieldNames": {
        "value": "id",
        "label": "nickname"
      }
    },
    "x-read-pretty": true
  }
  ```

## Collection 配置选项

在创建 collection 时，可以通过以下选项控制默认字段的生成：

```json
{
  "name": "collection_name",
  "title": "Collection Title",
  "autoGenId": true,      // 自动生成 ID 字段
  "createdAt": true,      // 添加创建时间字段
  "updatedAt": true,      // 添加更新时间字段
  "createdBy": true,      // 添加创建人字段
  "updatedBy": true,      // 添加更新人字段
  "fields": [
    // 需要显式添加所有默认字段到这里
  ]
}
```

## 重要说明

**NocoBase 的默认字段行为**：

1. **数据库层面**：当设置 `autoGenId: true`、`createdAt: true` 等选项时，NocoBase 会在数据库层面自动创建这些字段
2. **API 层面**：但是这些字段不会自动出现在 Collection API 的字段列表中
3. **显式添加**：如果需要在 Collection API 中看到这些字段，必须在 `fields` 数组中显式添加它们

## 完整示例

以下是一个包含所有默认字段的完整 collection 创建示例：

## 系统行为

1. **自动时间戳**: `createdAt` 和 `updatedAt` 字段会自动由数据库管理
2. **用户关联**: `createdBy` 和 `updatedBy` 字段会自动关联到当前登录用户
3. **外键字段**: 系统会自动创建 `createdById` 和 `updatedById` 外键字段
4. **只读显示**: 所有默认字段在 UI 中都设置为只读模式

## 测试验证

创建记录时的响应示例：
```json
{
  "data": {
    "id": 1,
    "name": "张三",
    "age": 12,
    "createdAt": "2025-08-03T14:18:17.247Z",
    "updatedAt": "2025-08-03T14:18:17.247Z",
    "createdById": 1,
    "updatedById": 1
  }
}
```

获取记录时包含关联用户信息：
```json
{
  "data": {
    "id": 1,
    "name": "李小明",
    "age": 11,
    "createdAt": "2025-08-03T14:22:58.171Z",
    "updatedAt": "2025-08-03T14:22:58.171Z",
    "createdById": 1,
    "updatedById": 1,
    "createdBy": {
      "id": 1,
      "nickname": "Super Admin",
      "username": "nocobase",
      "email": "<EMAIL>"
    },
    "updatedBy": {
      "id": 1,
      "nickname": "Super Admin",
      "username": "nocobase",
      "email": "<EMAIL>"
    }
  }
}
```

## API 查询验证

### 查看 Collection 的所有字段
```bash
curl -X GET "https://your-domain/api/collections:get?filterByTk=students&appends=fields"
```

### 获取记录时包含关联用户信息
```bash
curl -X GET "https://your-domain/api/students:get?filterByTk=1&appends=createdBy,updatedBy"
```

## 总结

1. **默认字段配置**：NocoBase 支持 5 个默认字段：id、createdAt、updatedAt、createdBy、updatedBy
2. **显式定义**：要在 Collection API 中看到这些字段，必须在创建时显式添加到 `fields` 数组中
3. **自动行为**：即使不显式添加，这些字段在数据库层面仍然存在并正常工作
4. **UI 配置**：每个默认字段都有对应的 UI Schema 配置，支持国际化
5. **关联关系**：createdBy 和 updatedBy 字段会自动关联到 users 表，并创建对应的外键字段
