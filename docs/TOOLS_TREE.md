### MCP Tools 清单（按模块分组）

- collections
  - `list_collections`, `get_collection`, `create_collection`, `update_collection`, `delete_collection`, `move_collection`
  - `set_collection_fields`
  - `get_field`, `list_fields`, `create_field`, `update_field`, `delete_field`, `move_field`

- collection-categories
  - `list_collection_categories`, `get_collection_category`, `create_collection_category`, `update_collection_category`, `delete_collection_category`, `move_collection_category`

- records
  - `list_records`, `get_record`, `create_record`, `update_record`, `delete_record`

- schema
  - `get_collection_schema`, `analyze_page_schema`, `get_schema_properties`

- routes
  - `list_routes`, `get_route`, `create_page_route`, `create_group_route`, `create_link_route`
  - `update_route`, `delete_route`, `move_route`
  - `create_page_route_fixed`, `fix_existing_route`

- blocks
  - `get_page_schema`, `list_page_blocks`
  - `add_table_block`, `add_form_block`, `add_markdown_block`, `add_details_block`, `add_kanban_block`, `add_list_block`, `add_grid_card_block`, `add_calendar_block`, `add_chart_block`
  - `remove_block`, `list_block_types`
  - 详情区块：`get_details_data`, `update_details_record`, `delete_details_record`, `export_details_data`, `bulk_update_details_records`, `configure_details_fields`, `add_details_action`, `add_association_details_block`, `custom_details_request`

- table-operations
  - `add_table_action`, `remove_table_action`, `update_table_action`, `list_table_actions`
  - `configure_table_column`, `configure_table_filter`, `configure_table_sort`
  - `send_custom_request`

- row-operations
  - `add_row_actions_column`, `add_row_action`
  - `create_record_advanced`, `update_record_advanced`, `association_operation`, `first_or_create`, `update_or_create`

- form-operations
  - `add_form_field`, `list_form_field_types`

- list-operations
  - `get_list_data`, `create_list_item`, `update_list_item`, `delete_list_item`, `bulk_delete_list_items`
  - `view_list_item`, `refresh_list`, `filter_list`
  - `add_list_action`, `configure_list_item_actions`, `configure_list_fields`
  - `export_list_data`, `import_list_data`, `custom_list_request`

- kanban-operations
  - `get_kanban_data`, `create_kanban_card`, `update_kanban_card`, `delete_kanban_card`, `move_kanban_card`
  - `batch_update_kanban_cards`, `add_kanban_action`
  - `configure_kanban_filter`, `configure_kanban_group_field`, `configure_kanban_sort_field`, `create_sort_field`

- grid-card-operations
  - `get_grid_card_data`, `create_grid_card_item`, `update_grid_card_item`, `delete_grid_card_item`, `view_grid_card_item`
  - `export_grid_card_data`, `import_grid_card_data`
  - `add_grid_card_action`, `configure_grid_card_item_actions`, `configure_grid_card_fields`
  - `filter_grid_card`, `grid_card_custom_request`

- calendar-operations
  - `get_calendar_data`, `create_calendar_event`, `update_calendar_event`, `delete_calendar_event`, `move_calendar_event`, `get_calendar_event`
  - `delete_recurring_event`, `batch_create_calendar_events`, `batch_update_calendar_events`
  - `add_calendar_action`, `configure_calendar_fields`, `update_calendar_settings`, `filter_calendar_events`

- markdown-operations
  - `update_markdown_content`, `get_markdown_content`, `set_markdown_template_engine`, `create_markdown_with_variables`, `parse_markdown_content`

- menu-operations
  - `create_menu_route`, `update_menu_route`, `delete_menu_route`, `move_menu_route`
  - `get_accessible_menu_routes`, `set_role_menu_permissions`
  - `create_menu_group`, `create_menu_page`, `create_menu_link`

- filter-operations
  - `add_filter_form_block`, `add_association_filter_block`, `configure_filter_targets`, `add_filter_field`, `execute_filter`, `reset_filter`, `get_filter_conditions`, `set_filter_default_values`

- users
  - `list_users`, `get_user`, `create_user`, `update_user`, `delete_user`

- roles
  - `list_roles`, `get_role`, `create_role`, `update_role`, `delete_role`, `check_role`, `set_default_role`
