# NocoBase MCP 看板操作工具使用示例

本文档提供了 NocoBase MCP 看板操作工具的详细使用示例和最佳实践。

## 🎯 完整工作流示例

### 1. 创建项目管理看板

```javascript
// 步骤1：创建任务集合（如果不存在）
await client.createCollection({
  name: 'project_tasks',
  title: '项目任务',
  fields: [
    {
      name: 'title',
      type: 'string',
      interface: 'input',
      title: '任务标题',
      required: true
    },
    {
      name: 'description',
      type: 'text',
      interface: 'textarea',
      title: '任务描述'
    },
    {
      name: 'status',
      type: 'string',
      interface: 'select',
      title: '状态',
      options: [
        { label: '待办', value: 'todo' },
        { label: '进行中', value: 'doing' },
        { label: '已完成', value: 'done' },
        { label: '已取消', value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      type: 'string',
      interface: 'select',
      title: '优先级',
      options: [
        { label: '低', value: 'low' },
        { label: '中', value: 'medium' },
        { label: '高', value: 'high' },
        { label: '紧急', value: 'urgent' }
      ]
    }
  ]
});

// 步骤2：创建排序字段
await client.createSortField('project_tasks', {
  fieldName: 'status_sort',
  fieldTitle: '状态排序',
  scopeKey: 'status'
});

// 步骤3：在页面中添加看板区块
await client.addKanbanBlock('page_grid_uid', {
  collectionName: 'project_tasks',
  title: '项目任务看板',
  groupField: 'status',
  sortField: 'status_sort'
});
```

### 2. 配置看板操作按钮

```javascript
// 添加筛选按钮
await client.addKanbanAction('kanban_block_uid', {
  actionType: 'filter',
  title: '筛选任务',
  icon: 'FilterOutlined',
  position: 'left'
});

// 添加新增按钮
await client.addKanbanAction('kanban_block_uid', {
  actionType: 'addNew',
  title: '新增任务',
  icon: 'PlusOutlined',
  position: 'right'
});

// 添加自定义导出按钮
await client.addKanbanAction('kanban_block_uid', {
  actionType: 'customRequest',
  title: '导出看板',
  icon: 'ExportOutlined',
  customSettings: {
    url: '/api/project_tasks/export',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
  }
});
```

### 3. 看板数据操作

```javascript
// 获取看板数据
const kanbanData = await client.getKanbanData('project_tasks', {
  groupField: 'status',
  sortField: 'status_sort',
  filter: {
    priority: { $in: ['high', 'urgent'] }
  },
  fields: ['id', 'title', 'description', 'status', 'priority'],
  appends: ['assignee', 'project']
});

// 创建新卡片
const newCard = await client.createKanbanCard('project_tasks', {
  title: '新功能开发',
  description: '开发用户管理模块',
  priority: 'high'
}, 'status', 'todo');

// 移动卡片到"进行中"列
await client.moveKanbanCard('project_tasks', {
  sourceId: newCard.id,
  targetScope: { status: 'doing' },
  sortField: 'status_sort'
});

// 更新卡片信息
await client.updateKanbanCard('project_tasks', newCard.id, {
  description: '开发用户管理模块 - 已开始编码',
  priority: 'urgent'
});
```

### 4. 批量操作示例

```javascript
// 批量将多个任务标记为完成
const completedTaskIds = [123, 456, 789];
await client.batchUpdateKanbanCards('project_tasks', completedTaskIds, {
  status: 'done',
  completedAt: new Date().toISOString()
});

// 批量更新优先级
const urgentTaskIds = [111, 222, 333];
await client.batchUpdateKanbanCards('project_tasks', urgentTaskIds, {
  priority: 'urgent'
});
```

### 5. 看板配置管理

```javascript
// 配置筛选条件
await client.configureKanbanFilter('kanban_block_uid', {
  $and: [
    { priority: { $in: ['high', 'urgent'] } },
    { assignee: { $ne: null } }
  ]
});

// 更改分组字段
await client.configureKanbanGroupField('kanban_block_uid', 'priority');

// 更改排序字段
await client.configureKanbanSortField('kanban_block_uid', 'created_at');
```

## 🔄 拖拽操作场景

### 场景1：同列内重新排序
```javascript
// 在同一列内调整卡片顺序
await client.moveKanbanCard('project_tasks', {
  sourceId: 123,
  targetId: 456, // 移动到卡片456的位置
  sortField: 'status_sort'
});
```

### 场景2：跨列移动
```javascript
// 将卡片从"待办"移动到"进行中"
await client.moveKanbanCard('project_tasks', {
  sourceId: 123,
  targetScope: { status: 'doing' },
  sortField: 'status_sort'
});
```

### 场景3：精确定位移动
```javascript
// 移动到指定列的指定位置
await client.moveKanbanCard('project_tasks', {
  sourceId: 123,
  targetId: 456,
  targetScope: { status: 'done' },
  sortField: 'status_sort'
});
```

## 🎨 高级配置示例

### 销售漏斗看板
```javascript
// 创建销售机会集合
await client.createCollection({
  name: 'sales_opportunities',
  title: '销售机会',
  fields: [
    { name: 'company', type: 'string', title: '公司名称' },
    { name: 'contact', type: 'string', title: '联系人' },
    { name: 'amount', type: 'number', title: '金额' },
    { name: 'stage', type: 'string', interface: 'select', title: '阶段',
      options: [
        { label: '潜在客户', value: 'lead' },
        { label: '初步接触', value: 'contact' },
        { label: '需求确认', value: 'qualified' },
        { label: '方案提交', value: 'proposal' },
        { label: '商务谈判', value: 'negotiation' },
        { label: '成交', value: 'closed_won' },
        { label: '失败', value: 'closed_lost' }
      ]
    }
  ]
});

// 创建销售漏斗看板
await client.addKanbanBlock('sales_page_grid', {
  collectionName: 'sales_opportunities',
  title: '销售漏斗',
  groupField: 'stage',
  sortField: 'amount'
});
```

### 内容管理看板
```javascript
// 创建文章集合
await client.createCollection({
  name: 'articles',
  title: '文章',
  fields: [
    { name: 'title', type: 'string', title: '标题' },
    { name: 'author', type: 'string', title: '作者' },
    { name: 'category', type: 'string', title: '分类' },
    { name: 'status', type: 'string', interface: 'select', title: '状态',
      options: [
        { label: '草稿', value: 'draft' },
        { label: '待审核', value: 'review' },
        { label: '已发布', value: 'published' },
        { label: '已下线', value: 'archived' }
      ]
    }
  ]
});

// 创建内容管理看板
await client.addKanbanBlock('content_page_grid', {
  collectionName: 'articles',
  title: '内容管理看板',
  groupField: 'status',
  sortField: 'created_at'
});

// 配置筛选器（只显示当前用户的文章）
await client.configureKanbanFilter('content_kanban_uid', {
  author: { $eq: '{{$user.id}}' }
});
```

## 🛠️ 错误处理和调试

### 常见错误处理
```javascript
try {
  await client.moveKanbanCard('project_tasks', {
    sourceId: 123,
    targetScope: { status: 'doing' }
  });
} catch (error) {
  if (error.status === 403) {
    console.error('权限不足，无法移动卡片');
  } else if (error.status === 404) {
    console.error('卡片或目标列不存在');
  } else {
    console.error('移动卡片失败:', error.message);
  }
}
```

### 调试工具
```javascript
// 获取看板区块的完整配置
const blockSchema = await client.getSchemaProperties('kanban_block_uid');
console.log('看板配置:', JSON.stringify(blockSchema, null, 2));

// 检查看板数据结构
const data = await client.getKanbanData('project_tasks', {
  groupField: 'status'
});
console.log('看板数据:', data);
```

## 📊 性能优化建议

### 1. 数据加载优化
```javascript
// 只获取必要的字段
await client.getKanbanData('project_tasks', {
  fields: ['id', 'title', 'status'], // 只获取显示必需的字段
  appends: ['assignee'] // 预加载关联数据
});
```

### 2. 批量操作优化
```javascript
// 使用批量更新而不是逐个更新
const cardIds = [123, 456, 789];
await client.batchUpdateKanbanCards('project_tasks', cardIds, {
  status: 'completed'
});
```

### 3. 筛选优化
```javascript
// 使用索引字段进行筛选
await client.configureKanbanFilter('kanban_uid', {
  status: { $ne: 'archived' }, // 排除已归档的任务
  assignee: { $ne: null } // 只显示已分配的任务
});
```

## 🔗 与其他工具的集成

### 与表格操作工具结合
```javascript
// 先创建看板，再添加相关的表格视图
await client.addKanbanBlock('page_grid', { /* kanban config */ });
await client.addTableBlock('page_grid', { /* table config */ });

// 在表格中添加"移动到看板"的操作
await client.addTableAction('table_uid', {
  actionType: 'custom',
  title: '移动到看板',
  customSettings: {
    url: '/api/project_tasks/move',
    method: 'POST'
  }
});
```

### 与表单操作工具结合
```javascript
// 配置看板卡片的编辑表单
await client.addFormAction('kanban_card_form_uid', {
  actionType: 'submit',
  title: '保存并移动',
  afterSubmit: 'moveToNextStage'
});
```

这些示例展示了如何使用新增的看板操作工具来构建完整的看板应用场景。通过这些工具，AI 客户端可以完全控制 NocoBase 看板的各个方面，从数据操作到界面配置。
