# NocoBase 行级别操作 MCP 工具指南

## 概述

基于 [nocobase-row-level-operations-api-mapping.md](../block-api-mappings/nocobase-row-level-operations-api-mapping.md) 文档，我们为 NocoBase MCP 服务器扩展了完整的行级别操作工具集。这些工具提供了对 NocoBase 记录的精细化控制能力。

## 🆕 新增工具列表

### 1. 表格行操作管理

#### 1.1 add_row_actions_column - 添加表格行操作列

为表格添加一个包含多个操作按钮的行操作列。

**参数：**
- `tableUid` (string): 表格区块的 UID
- `columnTitle` (string, 可选): 列标题，默认 "{{t('Actions')}}"
- `width` (number, 可选): 列宽度，默认 120
- `fixed` (enum, 可选): 固定位置 ('left' | 'right')
- `actions` (array, 可选): 预定义操作列表，默认 ['view', 'edit', 'delete']
- `customActions` (array, 可选): 自定义操作配置
- `position` (enum, 可选): 插入位置

**预定义操作类型：**
- `view` - 查看操作
- `edit` - 编辑操作
- `delete` - 删除操作
- `duplicate` - 复制操作
- `associate` - 关联操作
- `disassociate` - 取消关联操作

**示例：**
```json
{
  "tableUid": "users_table_123",
  "columnTitle": "{{t('Actions')}}",
  "width": 150,
  "fixed": "right",
  "actions": ["view", "edit", "delete"],
  "customActions": [
    {
      "name": "sendEmail",
      "title": "{{t('Send Email')}}",
      "action": "sendEmail",
      "icon": "MailOutlined",
      "type": "link",
      "useProps": "{{ useSendEmailActionProps }}"
    }
  ]
}
```

#### 1.2 add_row_action - 添加单个行操作按钮

向现有的行操作列添加单个操作按钮。

**参数：**
- `actionsColumnUid` (string): 行操作列的 UID
- `actionType` (enum): 操作类型
- `customConfig` (object, 可选): 自定义操作配置
- `position` (enum, 可选): 插入位置

### 2. 高级记录操作

#### 2.1 create_record_advanced - 高级记录创建

创建记录时支持字段白名单/黑名单控制和关联值更新。

**参数：**
- `collection` (string): 集合名称
- `values` (object): 记录数据
- `whitelist` (array, 可选): 允许的字段列表
- `blacklist` (array, 可选): 禁止的字段列表
- `updateAssociationValues` (boolean, 可选): 是否更新关联值

**示例：**
```json
{
  "collection": "users",
  "values": {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "secret123"
  },
  "whitelist": ["username", "email"],
  "updateAssociationValues": true
}
```

#### 2.2 update_record_advanced - 高级记录更新

更新记录时支持字段控制、强制更新和额外过滤条件。

**参数：**
- `collection` (string): 集合名称
- `id` (string|number): 记录 ID
- `values` (object): 更新数据
- `whitelist` (array, 可选): 允许的字段列表
- `blacklist` (array, 可选): 禁止的字段列表
- `updateAssociationValues` (boolean, 可选): 是否更新关联值
- `forceUpdate` (boolean, 可选): 强制更新
- `filter` (object, 可选): 额外过滤条件

### 3. 关联操作

#### 3.1 association_operation - 关联操作

执行记录间的关联操作（添加、移除、设置、切换）。

**参数：**
- `collection` (string): 源集合名称
- `sourceId` (string|number): 源记录 ID
- `associationField` (string): 关联字段名
- `operation` (enum): 操作类型 ('add' | 'remove' | 'set' | 'toggle')
- `filterByTk` (string|number, 可选): 目标记录 ID
- `filterByTks` (array, 可选): 多个目标记录 ID
- `values` (object, 可选): 关联值

**操作类型说明：**
- `add` - 添加关联
- `remove` - 移除关联
- `set` - 设置关联（替换现有关联）
- `toggle` - 切换关联状态

**示例：**
```json
{
  "collection": "users",
  "sourceId": 1,
  "associationField": "roles",
  "operation": "add",
  "filterByTk": 2
}
```

### 4. 高级查找操作

#### 4.1 first_or_create - 查找或创建

查找符合条件的记录，如果不存在则创建新记录。

**参数：**
- `collection` (string): 集合名称
- `values` (object): 查找或创建的数据
- `filter` (object, 可选): 查找条件

#### 4.2 update_or_create - 更新或创建

查找符合条件的记录并更新，如果不存在则创建新记录。

**参数：**
- `collection` (string): 集合名称
- `values` (object): 更新或创建的数据
- `filter` (object, 可选): 查找条件

## 🎯 使用场景

### 场景 1：完整的用户管理表格

```
AI: "为用户表格设置完整的行操作功能"

步骤：
1. 添加行操作列：add_row_actions_column
2. 配置查看、编辑、删除、复制操作
3. 添加自定义的"重置密码"操作
```

### 场景 2：精确的数据创建

```
AI: "创建用户时只允许设置基本信息，不允许直接设置权限"

使用：create_record_advanced
参数：whitelist: ["username", "email", "nickname"]
```

### 场景 3：用户角色管理

```
AI: "为用户分配管理员角色"

使用：association_operation
参数：operation: "add", associationField: "roles"
```

### 场景 4：数据去重

```
AI: "如果用户不存在则创建，存在则更新信息"

使用：update_or_create
参数：filter: { username: "specific_user" }
```

## 🔧 技术实现

### API 映射

| MCP 工具 | NocoBase API | 说明 |
|---------|-------------|------|
| `create_record_advanced` | `POST /{collection}:create` | 支持 whitelist/blacklist |
| `update_record_advanced` | `POST /{collection}:update` | 支持 forceUpdate |
| `association_operation` | `POST /{collection}/{id}/{field}:{op}` | 关联操作 |
| `first_or_create` | `POST /{collection}:firstOrCreate` | 查找或创建 |
| `update_or_create` | `POST /{collection}:updateOrCreate` | 更新或创建 |

### 行操作按钮 Schema

```typescript
{
  type: 'void',
  title: '操作',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  properties: {
    actions: {
      type: 'void',
      'x-decorator': 'DndContext',
      'x-component': 'Space',
      'x-component-props': { split: '|' },
      properties: {
        view: {
          type: 'void',
          title: "{{t('View')}}",
          'x-action': 'view',
          'x-component': 'Action.Link',
          'x-component-props': {
            icon: 'EyeOutlined',
            useProps: '{{ useViewActionProps }}'
          }
        }
        // 更多操作...
      }
    }
  }
}
```

## ⚠️ 注意事项

1. **字段控制**：whitelist 和 blacklist 不能同时使用
2. **关联操作**：确保关联字段存在且配置正确
3. **权限检查**：某些操作需要相应的 ACL 权限
4. **数据验证**：创建/更新时会执行字段验证规则
5. **性能考虑**：批量关联操作时注意性能影响

## 🧪 测试验证

运行测试脚本验证功能：

```bash
node scripts/test-row-operations.js
```

测试覆盖：
- ✅ 预定义行操作模板
- ✅ 行操作列添加
- ✅ 自定义行操作配置
- ✅ 高级记录创建/更新
- ✅ 关联操作
- ✅ 查找或创建操作

## 📚 相关文档

- **[nocobase-row-level-operations-api-mapping.md](../block-api-mappings/nocobase-row-level-operations-api-mapping.md)** - 原始 API 分析
- **[TABLE_OPERATION_TOOLS.md](./TABLE_OPERATION_TOOLS.md)** - 表格级别操作工具
- **[MCP_USAGE_GUIDE.md](./MCP_USAGE_GUIDE.md)** - MCP 使用指南

---

**更新日期**: 2025-01-09  
**版本**: v1.1.0  
**基于**: NocoBase 行级别操作 API 映射文档
