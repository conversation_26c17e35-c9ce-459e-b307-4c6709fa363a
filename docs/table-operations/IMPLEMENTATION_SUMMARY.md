# NocoBase 表格操作工具实现总结

## 概述

基于 [SWAGGER_API_ANALYSIS.md](./SWAGGER_API_ANALYSIS.md) 的深入分析，我们为 NocoBase MCP 服务器成功添加了完整的表格操作工具集。这些工具提供了对 NocoBase 表格区块的全面控制能力，包括操作按钮管理、列配置、筛选器设置、排序配置和自定义请求发送。

## 实现的功能

### 1. 核心 API 扩展

在 `src/client.ts` 中扩展了 NocoBaseClient 类，添加了以下底层 API 方法：

#### 通用 UI Schema 操作
- `insertAdjacentSchema()` - 插入相邻 Schema 节点
- `patchSchema()` - 更新 Schema 节点
- `removeSchema()` - 删除 Schema 节点
- `getSchemaJsonSchema()` - 获取 JSON Schema
- `sendCustomRequest()` - 发送自定义请求

#### 表格专用操作
- `addTableAction()` - 添加表格操作按钮
- `removeTableAction()` - 删除表格操作按钮
- `updateTableAction()` - 更新表格操作按钮
- `getTableActions()` - 获取表格操作按钮列表

### 2. 表格操作工具集

在 `src/tools/table-operations.ts` 中实现了 8 个专用工具：

#### 操作按钮管理
1. **add_table_action** - 添加表格操作按钮
2. **remove_table_action** - 删除表格操作按钮
3. **update_table_action** - 更新表格操作按钮
4. **list_table_actions** - 列出表格操作按钮

#### 表格配置管理
5. **configure_table_column** - 配置表格列
6. **configure_table_filter** - 配置表格筛选器
7. **configure_table_sort** - 配置表格排序

#### 自定义请求
8. **send_custom_request** - 发送自定义请求

### 3. 预定义操作模板

提供了 7 种预定义的操作模板：

- **create** - 新增操作（主要按钮，右对齐，需要 ACL）
- **filter** - 筛选操作（左对齐，无需 ACL）
- **bulkDelete** - 批量删除操作（右对齐，需要 ACL）
- **export** - 导出操作（右对齐，无需 ACL）
- **import** - 导入操作（右对齐，需要 ACL）
- **refresh** - 刷新操作（右对齐，无需 ACL）
- **customRequest** - 自定义请求操作（右对齐，无需 ACL）

## 技术实现细节

### API 端点映射

工具基于以下 NocoBase API 端点实现：

| 工具功能 | API 端点 | HTTP 方法 | 说明 |
|---------|----------|-----------|------|
| 插入操作按钮 | `/uiSchemas:insertAdjacent/{uid}` | POST | 在指定位置插入 Schema |
| 更新操作配置 | `/uiSchemas:patch` | POST | 更新 Schema 属性 |
| 删除操作按钮 | `/uiSchemas:remove/{uid}` | POST | 删除 Schema 节点 |
| 获取操作列表 | `/uiSchemas:getProperties/{uid}` | GET | 获取 Schema 属性 |
| 发送自定义请求 | `/customRequests:send/{id}` | POST | 执行自定义请求 |

### Schema 结构设计

#### 操作按钮 Schema
```typescript
{
  type: 'void',
  title: "{{t('Action Title')}}",
  'x-component': 'Action',
  'x-action': 'actionType',
  'x-designer': 'Action.Designer',
  'x-component-props': { /* 组件属性 */ },
  'x-decorator': 'ACLActionProvider', // 可选
  'x-acl-action': 'aclAction', // 可选
  'x-settings': 'actionSettings:settingName'
}
```

#### 表格列 Schema
```typescript
{
  type: 'void',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  properties: {
    [fieldName]: {
      type: 'string',
      'x-component': 'CollectionField',
      'x-read-pretty': true,
      title: 'Column Title'
    }
  },
  title: 'Column Title',
  width: 150,
  fixed: 'left' // 可选
}
```

### 错误处理机制

所有工具都实现了完整的错误处理：

1. **参数验证** - 使用 Zod 进行输入参数验证
2. **API 错误捕获** - 捕获并处理 HTTP 请求错误
3. **详细错误信息** - 返回具体的错误描述
4. **操作确认** - 成功操作后返回确认信息

### 权限控制支持

工具完全支持 NocoBase 的 ACL 权限控制：

- 通过 `requiresACL` 参数控制是否需要权限检查
- 通过 `aclAction` 参数指定具体权限动作
- 自动添加 `ACLActionProvider` 装饰器
- 支持细粒度的操作权限控制

## 文件结构

```
mcp-server-nocobase/
├── src/
│   ├── client.ts                    # 扩展了表格操作 API 方法
│   ├── tools/
│   │   └── table-operations.ts     # 新增的表格操作工具
│   └── index.ts                     # 注册了新工具
├── docs/table-operations/
│   ├── SWAGGER_API_ANALYSIS.md     # API 分析文档
│   ├── TABLE_OPERATION_TOOLS.md    # 工具使用文档
│   └── IMPLEMENTATION_SUMMARY.md   # 本实现总结
├── examples/
│   └── table-operations-demo.js    # 使用示例
└── scripts/
    └── test-table-operations.js    # 测试脚本
```

## 使用示例

### 基础操作按钮添加
```javascript
// 添加新增按钮
await client.addTableAction('table_uid', {
  title: "{{t('Add new')}}",
  action: 'create',
  icon: 'PlusOutlined',
  type: 'primary',
  align: 'right',
  requiresACL: true,
  aclAction: 'create'
});
```

### 自定义操作按钮
```javascript
// 添加自定义邮件发送按钮
await client.addTableAction('table_uid', {
  title: "{{t('Send Email')}}",
  action: 'sendEmail',
  icon: 'MailOutlined',
  type: 'default',
  align: 'right',
  requiresACL: true,
  aclAction: 'sendEmail'
});
```

### 表格列配置
```javascript
// 配置邮箱列
await client.insertAdjacentSchema('table_uid', {
  type: 'void',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  properties: {
    email: {
      type: 'string',
      'x-component': 'Input.Email',
      'x-read-pretty': true,
      title: '邮箱'
    }
  },
  title: '邮箱地址',
  width: 200,
  sortable: true
});
```

## 测试验证

### 测试脚本
- `scripts/test-table-operations.js` - 完整的功能测试
- `examples/table-operations-demo.js` - 使用示例演示

### 测试结果
✅ 所有 API 方法正确实现  
✅ 预定义操作模板正常工作  
✅ 错误处理机制完善  
✅ 工具注册成功  

## 扩展性设计

### 模板系统
- 支持添加新的预定义操作模板
- 支持自定义操作配置
- 支持组件属性扩展

### API 兼容性
- 基于 NocoBase 官方 API 实现
- 遵循 NocoBase Schema 规范
- 支持未来版本扩展

### 工具集成
- 与现有 MCP 工具无缝集成
- 支持工具链组合使用
- 提供完整的类型定义

## 总结

本次实现成功为 NocoBase MCP 服务器添加了完整的表格操作工具集，包括：

1. **8 个专用工具** - 覆盖表格操作的各个方面
2. **7 种预定义模板** - 快速添加常用操作
3. **完整的 API 扩展** - 底层 API 方法支持
4. **详细的文档** - 使用说明和示例
5. **测试验证** - 确保功能正确性

这些工具为用户提供了通过 MCP 协议完全控制 NocoBase 表格的能力，大大增强了 MCP 服务器的功能性和实用性。

---

**实现日期**: 2025-01-09  
**版本**: v1.0.0  
**实现者**: Claude Code Assistant  
**基于**: NocoBase Swagger API 分析
