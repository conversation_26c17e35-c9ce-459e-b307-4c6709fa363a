# NocoBase 行级别操作与 API 参数对应关系

本文档详细描述了 NocoBase 中各种行级别操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [基础 CRUD 操作](#1-基础-crud-操作)
2. [关联操作](#2-关联操作)
3. [高级操作](#3-高级操作)
4. [前端组件配置示例](#4-前端组件配置示例)
5. [参数传递机制](#5-参数传递机制)

---

## 1. 基础 CRUD 操作

### Create (创建)

- **x-action**: `create`
- **API 参数**:
  ```typescript
  {
    values: object,           // 要创建的数据
    whitelist?: string[],     // 允许的字段列表
    blacklist?: string[],     // 禁止的字段列表
    updateAssociationValues?: boolean  // 是否更新关联值
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/create.ts:15`

**实现逻辑**:
```typescript
export async function create(ctx: Context, next) {
  const repository = getRepositoryFromParams(ctx);
  const { whitelist, blacklist, updateAssociationValues, values } = ctx.action.params;

  const instance = await repository.create({
    values,
    whitelist,
    blacklist,
    updateAssociationValues,
    context: ctx,
  });

  ctx.body = instance;
  await next();
}
```

### Read (读取单条)

- **x-action**: `get`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filter?: object,               // 过滤条件
    fields?: string[],             // 返回字段
    appends?: string[],            // 关联字段
    except?: string[],             // 排除字段
    targetCollection?: string      // 目标集合
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/get.ts:12`

**实现逻辑**:
```typescript
export const get = proxyToRepository(
  ['filterByTk', 'fields', 'appends', 'except', 'filter', 'targetCollection'],
  'findOne',
);
```

### Update (更新)

- **x-action**: `update`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filter?: object,               // 过滤条件
    values: object,                // 更新的数据
    whitelist?: string[],          // 允许的字段列表
    blacklist?: string[],          // 禁止的字段列表
    updateAssociationValues?: boolean,  // 是否更新关联值
    forceUpdate?: boolean,         // 强制更新
    targetCollection?: string      // 目标集合
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/update.ts:12`

**实现逻辑**:
```typescript
export const update = proxyToRepository(
  [
    'filterByTk',
    'values',
    'whitelist',
    'blacklist',
    'filter',
    'updateAssociationValues',
    'forceUpdate',
    'targetCollection',
  ],
  'update',
);
```

### Delete (删除)

- **x-action**: `destroy`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filter?: object                // 过滤条件
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/destroy.ts:13`

**实现逻辑**:
```typescript
export async function destroy(ctx: Context, next) {
  const repository = getRepositoryFromParams(ctx);
  const { filterByTk, filter } = ctx.action.params;

  const instance = await repository.destroy({
    filter,
    filterByTk,
    context: ctx,
  });

  ctx.body = instance;
  await next();
}
```

### List (列表查询)

- **x-action**: `list`
- **API 参数**:
  ```typescript
  {
    page?: number,                 // 页码 (默认: 1)
    pageSize?: number,             // 每页条数 (默认: 20)
    paginate?: boolean,            // 是否分页 (默认: true)
    filter?: object,               // 过滤条件
    fields?: string[],             // 返回字段
    appends?: string[],            // 关联字段
    except?: string[],             // 排除字段
    sort?: string[],               // 排序
    tree?: boolean                 // 是否树形结构
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/list.ts:19`

**实现逻辑**:
```typescript
function findArgs(ctx: Context) {
  const params = ctx.action.params;
  const { fields, filter, appends, except, sort } = params;
  let { tree } = params;
  if (tree === true || tree === 'true') {
    tree = true;
  } else {
    tree = false;
  }
  return { tree, filter, fields, appends, except, sort };
}
```

---

## 2. 关联操作

### Add (添加关联)

- **x-action**: `add`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filterByTks?: any[],           // 多个主键值
    values?: any                   // 关联数据
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/add.ts:19`

**实现逻辑**:
```typescript
export async function add(ctx: Context, next) {
  const repository = getRepositoryFromParams(ctx);

  if (
    !(
      repository instanceof MultipleRelationRepository ||
      repository instanceof HasManyRepository ||
      repository instanceof ArrayFieldRepository
    )
  ) {
    return await next();
  }
  const filterByTk = ctx.action.params.filterByTk || ctx.action.params.filterByTks || ctx.action.params.values;

  await (<HasManyRepository | BelongsToManyRepository>repository).add(filterByTk);

  ctx.status = 200;
  await next();
}
```

### Remove (移除关联)

- **x-action**: `remove`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filterByTks?: any[],           // 多个主键值
    values?: any                   // 关联数据
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/remove.ts:11`

**实现逻辑**:
```typescript
export const remove = RelationRepositoryActionBuilder('remove');
```

### Set (设置关联)

- **x-action**: `set`
- **API 参数**:
  ```typescript
  {
    filterByTk?: string | number,  // 主键值
    filterByTks?: any[],           // 多个主键值
    values?: any                   // 关联数据
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/set.ts:11`

**实现逻辑**:
```typescript
export const set = RelationRepositoryActionBuilder('set');
```

### Toggle (切换关联)

- **x-action**: `toggle`
- **API 参数**:
  ```typescript
  {
    values: any                    // 要切换的关联值
  }
  ```

**源码位置**: `/packages/core/actions/src/actions/toggle.ts:14`

**实现逻辑**:
```typescript
export async function toggle(ctx: Context, next) {
  const repository = getRepositoryFromParams(ctx);

  if (!(repository instanceof BelongsToManyRepository)) {
    return await next();
  }

  await (<BelongsToManyRepository>repository).toggle(ctx.action.params.values);
  ctx.body = 'ok';
  await next();
}
```

---

## 3. 高级操作

### FirstOrCreate (查找或创建)

- **x-action**: `firstOrCreate`
- **API 参数**:
  ```typescript
  {
    values: object,                // 查找或创建的数据
    filter?: object                // 查找条件
  }
  ```

### UpdateOrCreate (更新或创建)

- **x-action**: `updateOrCreate`
- **API 参数**:
  ```typescript
  {
    values: object,                // 更新或创建的数据
    filter?: object                // 查找条件
  }
  ```

---

## 4. 前端组件配置示例

### 表格行操作按钮

```typescript
{
  type: 'void',
  title: '操作',
  'x-component': 'Table.Column',
  properties: {
    actions: {
      type: 'void',
      'x-component': 'Table.Column.Actions',
      properties: {
        edit: {
          type: 'void',
          title: '编辑',
          'x-action': 'update',
          'x-component': 'Action',
          'x-component-props': {
            useProps: '{{ useUpdateActionProps }}',
          },
        },
        delete: {
          type: 'void',
          title: '删除',
          'x-action': 'destroy',
          'x-component': 'Action',
          'x-component-props': {
            useProps: '{{ useDestroyActionProps }}',
          },
        },
      },
    },
  },
}
```

### 自定义操作按钮

```typescript
{
  type: 'void',
  title: '自定义操作',
  'x-action': 'customAction',
  'x-component': 'Action',
  'x-component-props': {
    useProps: '{{ useCustomActionProps }}',
  },
  properties: {
    modal: {
      type: 'void',
      'x-component': 'Action.Container',
      properties: {
        form: {
          type: 'void',
          'x-component': 'Form',
          properties: {
            // 表单字段
          },
        },
      },
    },
  },
}
```

### 关联操作组件

**源码位置**: `/packages/core/client/src/modules/actions/associate/AssociateActionInitializer.tsx:17`

```typescript
export const AssociateActionInitializer = (props) => {
  const schema = {
    type: 'void',
    title: '{{ t("Associate") }}',
    'x-action': 'associate',
    'x-component': 'Action',
    properties: {
      drawer: {
        type: 'void',
        'x-component': 'Action.Container',
        title: '{{ t("Select record") }}',
        'x-decorator': 'AssociateActionProvider',
        properties: {
          grid: {
            type: 'void',
            'x-component': 'Grid',
            'x-initializer': 'popup:tableSelector:addBlock',
          },
          footer: {
            'x-component': 'Action.Container.Footer',
            properties: {
              actions: {
                type: 'void',
                'x-component': 'ActionBar',
                properties: {
                  submit: {
                    title: '{{ t("Submit") }}',
                    'x-action': 'submit',
                    'x-component': 'Action',
                    'x-use-component-props': 'usePickActionProps',
                  },
                },
              },
            },
          },
        },
      },
    },
  };

  return <BlockInitializer {...itemConfig} schema={schema} item={itemConfig} />;
};
```

---

## 5. 参数传递机制

### 参数获取流程

```typescript
// 从上下文获取参数
const { filterByTk, filter, values } = ctx.action.params;

// 仓库选择
const repository = getRepositoryFromParams(ctx);

// 执行操作
const result = await repository.update({
  filterByTk,
  filter,
  values,
  context: ctx,
});
```

**源码位置**: `/packages/core/actions/src/utils.ts:26`

```typescript
export function getRepositoryFromParams(ctx: Context) {
  const { resourceName, sourceId, actionName } = ctx.action;

  if (sourceId === '_' && ['get', 'list'].includes(actionName)) {
    const collection = ctx.db.getCollection(resourceName);
    return ctx.db.getRepository<Repository>(collection.name);
  }

  if (sourceId) {
    return ctx.db.getRepository<MultipleRelationRepository>(resourceName, sourceId);
  }

  return ctx.db.getRepository<Repository>(resourceName);
}
```

### 参数代理机制

**源码位置**: `/packages/core/actions/src/actions/proxy-to-repository.ts:14`

```typescript
export function proxyToRepository(paramKeys: string[], repositoryMethod: string) {
  return async function (ctx: Context, next) {
    const repository = getRepositoryFromParams(ctx);

    const callObj = lodash.pick(ctx.action.params, paramKeys);
    callObj.context = ctx;

    ctx.body = await repository[repositoryMethod](callObj);

    ctx.status = 200;
    await next();
  };
}
```

### 关系仓库操作

**源码位置**: `/packages/core/actions/src/utils.ts:41`

```typescript
export function RelationRepositoryActionBuilder(method: 'remove' | 'set') {
  return async function (ctx: Context, next) {
    const repository = getRepositoryFromParams(ctx);

    const filterByTk = ctx.action.params.filterByTk || 
                      ctx.action.params.filterByTks || 
                      ctx.action.params.values;

    await repository[method](filterByTk);

    ctx.status = 200;
    await next();
  };
}
```

---

## 总结

本文档详细描述了 NocoBase 中所有主要的行级别操作，包括：

1. **基础 CRUD 操作**: create、get、update、destroy、list
2. **关联操作**: add、remove、set、toggle
3. **高级操作**: firstOrCreate、updateOrCreate
4. **前端组件配置**: 表格操作、自定义操作、关联操作
5. **参数传递机制**: 从前端到后端的完整数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的行级别操作功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析