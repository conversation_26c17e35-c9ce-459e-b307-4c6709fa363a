# NocoBase Collections API 快速参考

## 核心端点

### Collections 管理

```bash
# 获取集合列表（基础信息）
GET /api/collections:list

# 获取集合列表（包含所有字段详情）
GET /api/collections:listMeta

# 获取单个集合详情
GET /api/collections:get?filterByTk={collection_name}

# 获取集合详情（包含字段）
GET /api/collections:get?filterByTk={collection_name}&appends[]=fields

# 创建集合
POST /api/collections:create

# 更新集合
POST /api/collections:update?filterByTk={collection_name}

# 删除集合
POST /api/collections:destroy?filterByTk={collection_name}
```

### Fields 管理

```bash
# 获取集合的所有字段
GET /api/collections/{collection_name}/fields:list

# 获取单个字段详情
GET /api/collections/{collection_name}/fields:get?filterByTk={field_name}

# 为集合创建字段
POST /api/collections/{collection_name}/fields:create

# 更新字段
POST /api/collections/{collection_name}/fields:update?filterByTk={field_name}

# 删除字段
POST /api/collections/{collection_name}/fields:destroy?filterByTk={field_name}
```

## 常用查询参数

| 参数 | 描述 | 示例 |
|------|------|------|
| `filterByTk` | 按主键过滤 | `filterByTk=students` |
| `filter` | 条件过滤 | `filter[hidden]=false` |
| `appends` | 包含关联数据 | `appends[]=fields` |
| `sort` | 排序 | `sort[]=name` |
| `page` | 页码 | `page=1` |
| `pageSize` | 每页数量 | `pageSize=20` |

## 实用示例

### 1. 查看所有非隐藏集合
```bash
curl -X GET "https://your-domain/api/collections:list?filter[hidden]=false" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

### 2. 获取集合的完整信息（推荐使用 listMeta）
```bash
curl -X GET "https://your-domain/api/collections:listMeta" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

### 3. 查看特定集合的所有字段
```bash
curl -X GET "https://your-domain/api/collections/students/fields:list" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>"
```

### 4. 创建简单集合
```bash
curl -X POST "https://your-domain/api/collections:create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>" \
  -d '{
    "name": "products",
    "title": "产品",
    "autoGenId": true,
    "createdAt": true,
    "updatedAt": true,
    "fields": [
      {
        "name": "name",
        "type": "string",
        "interface": "input",
        "uiSchema": {
          "type": "string",
          "title": "产品名称",
          "x-component": "Input",
          "required": true
        }
      }
    ]
  }'
```

### 5. 为集合添加字段
```bash
curl -X POST "https://your-domain/api/collections/products/fields:create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -H "X-App: <app-name>" \
  -d '{
    "name": "price",
    "type": "decimal",
    "interface": "number",
    "uiSchema": {
      "type": "number",
      "title": "价格",
      "x-component": "InputNumber"
    }
  }'
```

## 响应格式

### 成功响应
```json
{
  "data": { /* 数据内容 */ },
  "meta": {
    "count": 10,
    "page": 1,
    "pageSize": 20,
    "totalPage": 1
  }
}
```

### 错误响应
```json
{
  "errors": [
    {
      "message": "错误信息描述"
    }
  ]
}
```

## 认证头部

所有请求都需要包含：

```http
Authorization: Bearer <your-jwt-token>
X-App: <app-name>
Content-Type: application/json
```

## 重要提示

1. **listMeta vs list**: 
   - `collections:list` 返回基础集合信息
   - `collections:listMeta` 返回包含所有字段的详细信息（推荐）

2. **字段查询**:
   - 使用 `collections/{name}/fields:list` 获取字段列表
   - 使用 `collections:get?appends[]=fields` 也可以获取字段信息

3. **权限要求**:
   - 查询操作通常需要读取权限
   - 创建/更新/删除操作需要管理员权限

4. **数据安全**:
   - 删除集合会删除所有相关数据
   - 建议在生产环境中谨慎使用删除操作

## 字段类型参考

常用字段类型：
- `string` - 字符串
- `integer` - 整数
- `decimal` - 小数
- `boolean` - 布尔值
- `date` - 日期时间
- `belongsTo` - 多对一关联
- `hasMany` - 一对多关联
- `belongsToMany` - 多对多关联

常用界面类型：
- `input` - 文本输入
- `textarea` - 多行文本
- `select` - 下拉选择
- `radioGroup` - 单选组
- `checkbox` - 复选框
- `email` - 邮箱输入
- `password` - 密码输入
