# NocoBase Calendar Operations - MCP工具文档

## 概述

本文档介绍了为NocoBase MCP服务器新增的Calendar Block相关操作工具。这些工具提供了完整的日历事件管理功能，包括事件的CRUD操作、移动、筛选、批量处理以及日历配置管理。

## 新增功能

### 1. 客户端API方法 (client.ts)

在`NocoBaseClient`类中新增了以下方法：

- `getCalendarData()` - 获取日历数据，支持筛选和日期范围
- `createCalendarEvent()` - 创建日历事件
- `updateCalendarEvent()` - 更新日历事件
- `deleteCalendarEvent()` - 删除日历事件
- `moveCalendarEvent()` - 移动日历事件到新时间
- `deleteRecurringEvent()` - 处理重复事件删除
- `getCalendarEvent()` - 获取单个事件详情
- `batchCreateCalendarEvents()` - 批量创建事件
- `batchUpdateCalendarEvents()` - 批量更新事件
- `addCalendarAction()` - 添加日历操作按钮
- `configureCalendarFields()` - 配置字段映射
- `updateCalendarSettings()` - 更新日历设置

### 2. MCP工具 (calendar-operations.ts)

新增了以下MCP工具：

#### 数据操作工具
- `get_calendar_data` - 获取日历数据
- `get_calendar_event` - 获取单个事件详情
- `filter_calendar_events` - 筛选日历事件

#### 事件CRUD工具
- `create_calendar_event` - 创建日历事件
- `update_calendar_event` - 更新日历事件
- `delete_calendar_event` - 删除日历事件
- `move_calendar_event` - 移动日历事件

#### 批量操作工具
- `batch_create_calendar_events` - 批量创建事件
- `batch_update_calendar_events` - 批量更新事件

#### 重复事件工具
- `delete_recurring_event` - 处理重复事件删除

#### 配置管理工具
- `add_calendar_action` - 添加日历操作按钮
- `configure_calendar_fields` - 配置字段映射
- `update_calendar_settings` - 更新日历设置

### 3. 增强的Calendar Block模板

更新了`createCalendarBlockSchema`函数，添加了：
- 完整的事件查看器配置
- 支持弹窗模式的事件详情
- 标签页支持
- 可配置的操作栏

## 主要特性

### 1. 完整的事件生命周期管理
- 支持事件的创建、查看、编辑、删除
- 支持事件的拖拽移动
- 支持批量操作

### 2. 重复事件支持
- 支持cron表达式定义的重复事件
- 支持排除特定日期（exclude数组）
- 支持删除所有重复事件或特定日期

### 3. 灵活的字段映射
- 支持自定义字段名称映射
- 支持颜色字段配置
- 支持开始/结束时间字段配置

### 4. 丰富的筛选功能
- 支持复杂的筛选条件
- 支持日期范围筛选
- 支持关联字段查询

### 5. 可配置的日历设置
- 支持农历显示
- 支持默认视图设置
- 支持快速创建事件开关
- 支持一周开始日配置

## 使用示例

### 创建日历事件
```json
{
  "tool": "create_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventData": {
      "title": "团队会议",
      "description": "每周例会",
      "startDate": "2024-01-15T09:00:00.000Z",
      "endDate": "2024-01-15T10:00:00.000Z",
      "priority": "high"
    }
  }
}
```

### 移动事件
```json
{
  "tool": "move_calendar_event",
  "arguments": {
    "collectionName": "events",
    "eventId": 1,
    "newTimes": {
      "start": "2024-01-16T09:00:00.000Z",
      "end": "2024-01-16T10:00:00.000Z"
    }
  }
}
```

### 筛选事件
```json
{
  "tool": "filter_calendar_events",
  "arguments": {
    "collectionName": "events",
    "filterConditions": {
      "priority": "high"
    },
    "dateRange": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-31T23:59:59.999Z"
    }
  }
}
```

### 配置日历字段
```json
{
  "tool": "configure_calendar_fields",
  "arguments": {
    "calendarUid": "calendar_uid_here",
    "fieldNames": {
      "title": "eventTitle",
      "start": "startTime",
      "end": "endTime",
      "colorFieldName": "category"
    }
  }
}
```

## API映射关系

根据参考文档，工具与NocoBase API的映射关系如下：

| 工具 | API端点 | HTTP方法 | 权限要求 |
|------|---------|----------|----------|
| get_calendar_data | /{collection}:list | GET | list |
| create_calendar_event | /{collection}:create | POST | create |
| update_calendar_event | /{collection}:update | PUT | update |
| delete_calendar_event | /{collection}:destroy | DELETE | destroy |
| move_calendar_event | /{collection}:update | PUT | update |
| get_calendar_event | /{collection}:get | GET | get |

## 重复事件处理

重复事件通过以下机制处理：
1. `cron`字段定义重复模式（every_week, every_month, every_year）
2. `exclude`数组排除特定日期
3. 客户端根据cron表达式生成重复事件实例

## 权限控制

所有calendar操作都遵循NocoBase的ACL权限系统：
- 创建事件需要`create`权限
- 查看事件需要`get`或`list`权限
- 更新事件需要`update`权限
- 删除事件需要`destroy`权限

## 错误处理

所有工具都包含完善的错误处理：
- API调用失败时返回详细错误信息
- 参数验证失败时提供清晰的错误提示
- 权限不足时返回相应的权限错误

## 测试

详细的测试用例请参考 `calendar-operations.test.md` 文件。

## 注意事项

1. 日历事件的时间字段应使用ISO 8601格式
2. 重复事件的cron字段支持特定的值：every_week, every_month, every_year
3. 字段映射配置需要与实际集合字段匹配
4. 批量操作时注意性能影响，建议分批处理大量数据

## 兼容性

这些工具与NocoBase最新版本兼容，基于官方Calendar Block插件的API设计。
