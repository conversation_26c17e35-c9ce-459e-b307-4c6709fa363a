import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端 - 处理与服务器的通信
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            console.log('📥 Raw output:', line);
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    console.log('📥 Response:', JSON.stringify(response, null, 2));
    
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 10000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 Sending ${method} request:`, JSON.stringify(request, null, 2));
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function testBlockOperations() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== NocoBase 区块管理测试 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    });

    // 2. 列出工具
    console.log('\n📋 Step 2: List tools');
    const tools = await mcpClient.sendRequest('tools/list');
    console.log('Available tools:', tools.tools?.map(t => t.name).join(', '));

    // 3. 列出区块类型
    console.log('\n📋 Step 3: List block types');
    await mcpClient.sendRequest('tools/call', {
      name: 'list_block_types',
      arguments: {}
    });

    // 4. 列出现有路由（找一个页面来添加区块）
    console.log('\n📋 Step 4: List routes to find a page');
    await mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: { tree: true }
    });

    // 注意：在实际使用中，你需要从上面的响应中获取一个页面的 UID
    // 这里我们使用一个示例 UID，实际测试时需要替换为真实的页面 UID
    const examplePageUid = 'example-page-uid';

    // 5. 获取页面 Schema（如果有真实的页面 UID）
    console.log('\n📋 Step 5: Get page schema (示例)');
    try {
      await mcpClient.sendRequest('tools/call', {
        name: 'get_page_schema',
        arguments: { schemaUid: examplePageUid }
      });
    } catch (error) {
      console.log('⚠️ Expected error for example UID:', error.message);
    }

    // 6. 添加 Markdown 区块（不需要集合）
    console.log('\n📋 Step 6: Add Markdown block (示例)');
    try {
      await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: examplePageUid,
          title: '测试 Markdown 区块',
          content: '# 这是一个测试区块\n\n这是通过 MCP 工具创建的 Markdown 区块。'
        }
      });
    } catch (error) {
      console.log('⚠️ Expected error for example UID:', error.message);
    }

    // 7. 列出集合（为数据区块做准备）
    console.log('\n📋 Step 7: List collections');
    await mcpClient.sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });

    console.log('\n✅ 区块管理功能测试完成！');
    console.log('\n📝 注意事项：');
    console.log('- 要实际添加区块，需要提供真实的页面 UID');
    console.log('- 页面 UID 可以通过 list_routes 或 get_page_schema 获取');
    console.log('- 数据区块需要指定有效的集合名称');
    console.log('- 区块会被添加到页面的 Grid 容器中');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动测试
testBlockOperations().catch(console.error);
