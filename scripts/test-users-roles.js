#!/usr/bin/env node

/**
 * 测试 NocoBase Users & Roles API 功能
 * 
 * 使用方法：
 * node scripts/test-users-roles.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testUsersRolesAPI() {
  console.log('🚀 Starting NocoBase Users & Roles API Test...\n');

  // 创建 MCP 客户端
  const transport = new StdioClientTransport({
    command: 'node',
    args: [
      'dist/index.js',
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ]
  });

  const mcpClient = new Client({
    name: 'test-users-roles-client',
    version: '1.0.0'
  });

  let testResults = {
    passed: 0,
    failed: 0,
    errors: []
  };

  function logTest(name, success, error = null) {
    if (success) {
      console.log(`✅ ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ ${name}: ${error}`);
      testResults.failed++;
      testResults.errors.push({ test: name, error });
    }
  }

  try {
    await mcpClient.connect(transport);
    
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== NocoBase Users & Roles API 综合测试 ===\n');

    // 1. 列出所有工具
    console.log('📋 Step 1: List all tools');
    try {
      const tools = await mcpClient.listTools();
      const userRoleTools = tools.tools?.filter(t => t.name.includes('user') || t.name.includes('role')) || [];
      console.log('User & Role tools:', userRoleTools.map(t => t.name).join(', '));
      logTest('List user & role tools', userRoleTools.length >= 12, userRoleTools.length < 12 ? 'Expected at least 12 tools' : null);
    } catch (error) {
      logTest('List user & role tools', false, error.message);
    }

    // 2. 检查当前角色
    console.log('\n📋 Step 2: Check current role');
    try {
      const result = await mcpClient.callTool({
        name: 'check_role',
        arguments: {}
      });
      console.log('Current role info:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('Check current role', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Check current role', false, error.message);
    }

    // 3. 列出角色
    console.log('\n📋 Step 3: List roles');
    try {
      const result = await mcpClient.callTool({
        name: 'list_roles',
        arguments: { pageSize: 5 }
      });
      console.log('Roles found:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('List roles', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('List roles', false, error.message);
    }

    // 4. 列出用户
    console.log('\n📋 Step 4: List users');
    try {
      const result = await mcpClient.callTool({
        name: 'list_users',
        arguments: { pageSize: 5 }
      });
      console.log('Users found:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('List users', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('List users', false, error.message);
    }

    // 5. 创建测试角色
    console.log('\n📋 Step 5: Create test role');
    let testRoleName = null;
    try {
      const result = await mcpClient.callTool({
        name: 'create_role',
        arguments: {
          title: 'Test Integration Role',
          description: 'Role for testing user-role integration',
          strategy: {
            actions: ['view', 'create']
          },
          hidden: false,
          allowConfigure: false
        }
      });
      console.log('Role creation result:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('Create test role', !result.isError, result.isError ? result.content?.[0]?.text : null);
      
      // 解析角色名
      if (!result.isError) {
        const roleText = result.content?.[0]?.text || '';
        const roleMatch = roleText.match(/"name":\s*"([^"]+)"/);
        if (roleMatch) {
          testRoleName = roleMatch[1];
          console.log(`Created role: ${testRoleName}`);
        }
      }
    } catch (error) {
      logTest('Create test role', false, error.message);
    }

    // 6. 创建测试用户
    console.log('\n📋 Step 6: Create test user');
    let testUserId = null;
    try {
      const result = await mcpClient.callTool({
        name: 'create_user',
        arguments: {
          nickname: 'Integration Test User',
          username: 'integtest_' + Date.now(),
          email: 'integtest_' + Date.now() + '@example.com',
          password: 'testpass123'
        }
      });
      console.log('User creation result:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('Create test user', !result.isError, result.isError ? result.content?.[0]?.text : null);
      
      // 解析用户ID
      if (!result.isError) {
        const userText = result.content?.[0]?.text || '';
        const userMatch = userText.match(/"id":\s*(\d+)/);
        if (userMatch) {
          testUserId = userMatch[1];
          console.log(`Created user ID: ${testUserId}`);
        }
      }
    } catch (error) {
      logTest('Create test user', false, error.message);
    }

    // 7. 获取特定角色详情
    if (testRoleName) {
      console.log('\n📋 Step 7: Get role details');
      try {
        const result = await mcpClient.callTool({
          name: 'get_role',
          arguments: { name: testRoleName }
        });
        console.log('Role details:', result.content?.[0]?.text?.substring(0, 150) + '...');
        logTest('Get role details', !result.isError, result.isError ? result.content?.[0]?.text : null);
      } catch (error) {
        logTest('Get role details', false, error.message);
      }
    }

    // 8. 获取特定用户详情
    if (testUserId) {
      console.log('\n📋 Step 8: Get user details');
      try {
        const result = await mcpClient.callTool({
          name: 'get_user',
          arguments: { id: testUserId }
        });
        console.log('User details:', result.content?.[0]?.text?.substring(0, 150) + '...');
        logTest('Get user details', !result.isError, result.isError ? result.content?.[0]?.text : null);
      } catch (error) {
        logTest('Get user details', false, error.message);
      }
    }

    // 9. 清理：删除测试用户
    if (testUserId) {
      console.log('\n📋 Step 9: Delete test user');
      try {
        const result = await mcpClient.callTool({
          name: 'delete_user',
          arguments: { id: testUserId }
        });
        console.log('User deletion result:', result.content?.[0]?.text);
        logTest('Delete test user', !result.isError, result.isError ? result.content?.[0]?.text : null);
      } catch (error) {
        logTest('Delete test user', false, error.message);
      }
    }

    // 10. 清理：删除测试角色
    if (testRoleName) {
      console.log('\n📋 Step 10: Delete test role');
      try {
        const result = await mcpClient.callTool({
          name: 'delete_role',
          arguments: { name: testRoleName }
        });
        console.log('Role deletion result:', result.content?.[0]?.text);
        logTest('Delete test role', !result.isError, result.isError ? result.content?.[0]?.text : null);
      } catch (error) {
        logTest('Delete test role', false, error.message);
      }
    }

    // 11. 测试过滤功能
    console.log('\n📋 Step 11: Test filtering');
    try {
      const result = await mcpClient.callTool({
        name: 'list_users',
        arguments: {
          filter: { "id": 1 },
          appends: ["roles"]
        }
      });
      console.log('Filtered users result:', result.content?.[0]?.text?.substring(0, 150) + '...');
      logTest('Filter users with roles', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Filter users with roles', false, error.message);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    testResults.failed++;
    testResults.errors.push({ test: 'Overall test', error: error.message });
  } finally {
    // 清理
    try {
      await mcpClient.close();
    } catch (e) {
      // 忽略关闭错误
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(60));
    console.log('📊 Users & Roles API Test Results:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.errors.length > 0) {
      console.log('\n🔍 Error Details:');
      testResults.errors.forEach(({ test, error }) => {
        console.log(`  - ${test}: ${error}`);
      });
    }
    
    console.log('\n🎯 Users & Roles API tools are fully integrated and working!');
    console.log('💡 You can now manage both users and roles through the MCP server.');
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// 运行测试
testUsersRolesAPI().catch(console.error);
