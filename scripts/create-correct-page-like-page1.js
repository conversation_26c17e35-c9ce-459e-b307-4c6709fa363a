#!/usr/bin/env node

// 创建一个和 page_1 结构相同的页面：主页面 + tabs 子路由
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let mainPageRoute = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // 第一步：创建主页面（简单的 Page 组件，像 page_1 一样）
          const pageTitle = `正确的 MCP 页面 ${Date.now()}`;
          console.log('创建主页面:', pageTitle);
          send(server, { 
            jsonrpc: '2.0', 
            id: 2, 
            method: 'tools/call', 
            params: { 
              name: 'create_page_route', 
              arguments: { 
                title: pageTitle, 
                template: 'blank', // 使用空白模板，不要预设 Grid
                icon: 'ExperimentOutlined',
                enableTabs: true // 启用 tabs
              } 
            } 
          });
        } else if (msg.id === 2) {
          console.log('主页面创建结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 解析创建的路由信息
          const text = content.find(item => item.type === 'text')?.text || '';
          const match = text.match(/"id":\s*(\d+)/);
          if (match) {
            const routeId = parseInt(match[1]);
            mainPageRoute = { id: routeId };
            console.log('主页面路由 ID:', routeId);
            
            // 第二步：为主页面创建一个 tabs 子路由
            console.log('创建 tabs 子路由...');
            send(server, {
              jsonrpc: '2.0',
              id: 3,
              method: 'tools/call',
              params: {
                name: 'create_tabs_route', // 需要实现这个工具
                arguments: {
                  parentId: routeId,
                  tabSchemaName: `tab_${Date.now()}`
                }
              }
            });
          } else {
            console.log('无法解析路由 ID');
            server.kill();
          }
        } else if (msg.id === 3) {
          if (msg.error) {
            console.log('create_tabs_route 工具不存在，需要手动创建 tabs 子路由');
            // 手动创建 tabs 子路由的逻辑
            console.log('尝试手动创建...');
            server.kill();
          } else {
            console.log('tabs 子路由创建结果:');
            const content = msg.result?.content || [];
            for (const item of content) if (item?.type === 'text') console.log(item.text);
            server.kill();
          }
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'create-correct-page', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
