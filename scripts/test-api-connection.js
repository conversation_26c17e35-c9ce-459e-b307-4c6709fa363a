#!/usr/bin/env node

/**
 * 测试 API 连接和基本功能
 */

const { NocoBaseClient } = require('../dist/client.js');

async function testConnection() {
  console.log('🔗 测试 NocoBase API 连接...\n');
  
  const client = new NocoBaseClient({
    baseUrl: 'https://n.astra.xin/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
    app: 'mcp_playground'
  });

  try {
    // 1. 测试基本连接
    console.log('📡 测试基本连接...');
    const collections = await client.listCollections();
    console.log(`✅ 连接成功! 找到 ${collections.length} 个集合`);
    
    // 2. 测试页面 Schema 获取
    console.log('\n📄 测试页面 Schema 获取...');
    const pageSchema = await client.getPageSchema('cafh7yoyd6w');
    console.log('✅ 页面 Schema 获取成功');
    console.log(`📊 Schema 类型: ${pageSchema.type}, 组件: ${pageSchema['x-component']}`);
    
    // 3. 查找 Grid 容器
    console.log('\n🔍 查找 Grid 容器...');
    function findGrids(obj, path = '') {
      const grids = [];
      if (obj && typeof obj === 'object') {
        if (obj['x-component'] === 'Grid') {
          grids.push({
            uid: obj['x-uid'],
            path: path,
            hasProperties: !!obj.properties,
            propertiesCount: obj.properties ? Object.keys(obj.properties).length : 0
          });
        }
        if (obj.properties) {
          for (const [key, value] of Object.entries(obj.properties)) {
            grids.push(...findGrids(value, path ? `${path}.${key}` : key));
          }
        }
      }
      return grids;
    }
    
    const grids = findGrids(pageSchema);
    console.log(`✅ 找到 ${grids.length} 个 Grid 容器`);
    
    if (grids.length > 0) {
      console.log('📋 Grid 容器列表:');
      grids.forEach((grid, index) => {
        console.log(`  ${index + 1}. UID: ${grid.uid}, 路径: ${grid.path}, 属性数: ${grid.propertiesCount}`);
      });
      
      // 选择主要的 Grid (通常是第一个有属性的)
      const mainGrid = grids.find(g => g.propertiesCount > 0) || grids[0];
      console.log(`\n🎯 选择主要 Grid: ${mainGrid.uid}`);
      
      return {
        success: true,
        mainGridUid: mainGrid.uid,
        collections: collections.map(c => c.name),
        gridsCount: grids.length
      };
    } else {
      throw new Error('未找到任何 Grid 容器');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
testConnection().then(result => {
  if (result.success) {
    console.log('\n🎉 所有测试通过!');
    console.log('📊 测试结果:', {
      mainGrid: result.mainGridUid,
      collections: result.collections,
      gridsCount: result.gridsCount
    });
  } else {
    console.log('\n💥 测试失败:', result.error);
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 测试运行器崩溃:', error);
  process.exit(1);
});
