import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 ${method}:`, params.name || method);
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function addRealBlocks() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🎨 在真实页面上添加区块 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'real-block-client', version: '1.0.0' }
    });

    // 2. 获取页面的真实 Schema
    const pageUid = 'page-1754235700602-luqwmsxu9';
    console.log(`\n📋 Step 2: Get real page schema for: ${pageUid}`);
    
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: pageUid }
    });

    // 解析 Schema 找到 Grid UID
    let gridUid = null;
    if (schemaResult && schemaResult.content && schemaResult.content[0]) {
      const text = schemaResult.content[0].text;
      console.log('📄 Page schema response:', text.substring(0, 200) + '...');
      
      // 尝试解析 JSON
      const match = text.match(/Page schema retrieved successfully:\s*(\{[\s\S]*\})/);
      if (match) {
        try {
          const schema = JSON.parse(match[1]);
          
          // 递归查找 Grid 组件
          function findGrid(obj, path = '') {
            if (obj && typeof obj === 'object') {
              if (obj['x-component'] === 'Grid') {
                console.log(`✅ Found Grid at path: ${path}, UID: ${obj['x-uid']}`);
                return obj['x-uid'];
              }
              if (obj.properties) {
                for (const key in obj.properties) {
                  const result = findGrid(obj.properties[key], `${path}.${key}`);
                  if (result) return result;
                }
              }
            }
            return null;
          }
          
          gridUid = findGrid(schema, 'root');
        } catch (e) {
          console.log('⚠️ Failed to parse schema JSON:', e.message);
        }
      }
    }

    if (!gridUid) {
      console.log('⚠️ No Grid container found, the page might be completely empty');
      console.log('💡 This is normal for a new blank page - NocoBase creates the Grid when you add the first block');
      
      // 对于空白页面，我们需要先创建基础结构
      console.log('\n📋 Step 3: Create basic page structure');
      
      // 尝试使用页面本身的 UID 作为父容器
      gridUid = pageUid;
      console.log(`🔧 Using page UID as parent: ${gridUid}`);
    }

    // 3. 获取可用集合
    console.log('\n📋 Step 4: Get available collections');
    const collectionsResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });

    let collections = [];
    if (collectionsResult && collectionsResult.content && collectionsResult.content[0]) {
      const text = collectionsResult.content[0].text;
      const collectionMatches = text.match(/• (\w+) \(/g);
      if (collectionMatches) {
        collections = collectionMatches.map(m => m.replace(/• (\w+) \(/, '$1'));
      }
    }
    console.log(`✅ Available collections: ${collections.join(', ')}`);

    // 4. 开始添加区块
    console.log('\n📋 Step 5: Adding blocks to the page');

    // 4.1 添加欢迎 Markdown 区块
    console.log('\n🔸 Adding welcome Markdown block...');
    try {
      const markdownResult = await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: gridUid,
          title: '🎉 欢迎使用 NocoBase MCP 区块系统',
          content: `# 🚀 NocoBase 区块管理演示

## 🎯 这个页面刚刚通过 MCP 工具自动创建了区块！

### 📊 功能展示
- ✅ **Markdown 区块** - 您正在看的这个区块
- ✅ **数据表格区块** - 下方的数据管理区块  
- ✅ **表单区块** - 数据录入区块
- ✅ **程序化创建** - 全部通过 API 自动生成

### 🛠️ 技术特点
1. **实时创建** - 区块立即出现在页面上
2. **标准配置** - 使用 NocoBase 标准区块模板
3. **完整功能** - 支持所有原生区块功能
4. **批量操作** - 可以批量创建多个区块

---
*创建时间: ${new Date().toLocaleString('zh-CN')}*  
*通过 NocoBase MCP 服务器自动生成*`,
          position: 'beforeEnd'
        }
      });
      
      if (markdownResult && markdownResult.content) {
        console.log('✅ Markdown block added successfully!');
      }
    } catch (error) {
      console.log('❌ Failed to add Markdown block:', error.message);
    }

    // 4.2 如果有集合，添加数据区块
    if (collections.length > 0) {
      const collection = collections[0]; // 使用第一个集合
      
      // 添加表格区块
      console.log(`\n🔸 Adding Table block for collection: ${collection}`);
      try {
        await mcpClient.sendRequest('tools/call', {
          name: 'add_table_block',
          arguments: {
            parentUid: gridUid,
            collectionName: collection,
            title: `📊 ${collection} 数据管理`,
            position: 'beforeEnd'
          }
        });
        console.log('✅ Table block added successfully!');
      } catch (error) {
        console.log('❌ Failed to add Table block:', error.message);
      }

      // 添加表单区块
      console.log(`\n🔸 Adding Form block for collection: ${collection}`);
      try {
        await mcpClient.sendRequest('tools/call', {
          name: 'add_form_block',
          arguments: {
            parentUid: gridUid,
            collectionName: collection,
            title: `📝 新建 ${collection} 记录`,
            type: 'create',
            position: 'beforeEnd'
          }
        });
        console.log('✅ Form block added successfully!');
      } catch (error) {
        console.log('❌ Failed to add Form block:', error.message);
      }
    }

    // 5. 验证结果
    console.log('\n📋 Step 6: Verify results');
    try {
      await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: pageUid }
      });
    } catch (error) {
      console.log('⚠️ Could not list blocks:', error.message);
    }

    // 6. 完成
    console.log('\n🎊 区块添加完成！');
    console.log('\n📱 请刷新页面查看效果：');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/page-1754235700602-luqwmsxu9`);
    
    console.log('\n💡 您应该能看到：');
    console.log('   1. 📖 欢迎 Markdown 区块 - 包含说明文档');
    console.log('   2. 📊 数据表格区块 - 显示集合数据');
    console.log('   3. 📝 表单区块 - 用于创建新记录');
    
    console.log('\n🚀 这证明了 MCP 工具可以：');
    console.log('   ✅ 在真实页面上添加区块');
    console.log('   ✅ 区块立即生效并可用');
    console.log('   ✅ 支持各种类型的区块');
    console.log('   ✅ 完全程序化的区块管理');

  } catch (error) {
    console.error('❌ Failed to add blocks:', error);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动实际区块添加
addRealBlocks().catch(console.error);
