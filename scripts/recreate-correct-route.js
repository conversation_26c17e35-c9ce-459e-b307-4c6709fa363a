import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 重新创建正确的路由
async function recreateCorrectRoute() {
  console.log('🔄 重新创建正确的路由\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 删除有问题的 Student Management 路由
    console.log('📋 1. 删除有问题的 Student Management 路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    const studentRoute = routes.find(r => r.title === 'Student Management');
    if (studentRoute) {
      try {
        await client.post(`/desktopRoutes:destroy?filterByTk=${studentRoute.id}`);
        console.log(`   ✅ 删除路由成功: ID ${studentRoute.id}`);
      } catch (error) {
        console.log(`   ❌ 删除路由失败: ${error.response?.data?.message || error.message}`);
      }
    } else {
      console.log('   ℹ️ 未找到 Student Management 路由');
    }

    // 2. 删除其他有问题的路由（标题为 null 的）
    console.log('\n📋 2. 清理其他有问题的路由');
    const problematicRoutes = routes.filter(route => 
      route.title === 'null' || route.title === null
    );
    
    console.log(`   发现 ${problematicRoutes.length} 个有问题的路由`);
    for (const route of problematicRoutes) {
      try {
        // 先尝试删除对应的 UI Schema
        if (route.schemaUid) {
          try {
            await client.post('/uiSchemas:remove', {
              'x-uid': route.schemaUid
            });
            console.log(`   ✅ 删除 UI Schema: ${route.schemaUid}`);
          } catch (schemaError) {
            console.log(`   ⚠️ 删除 UI Schema 失败: ${route.schemaUid}`);
          }
        }
        
        // 删除路由
        await client.post(`/desktopRoutes:destroy?filterByTk=${route.id}`);
        console.log(`   ✅ 删除有问题的路由: ID ${route.id}`);
      } catch (error) {
        console.log(`   ❌ 删除路由失败: ID ${route.id}`);
      }
    }

    // 3. 使用正确的方式创建新路由
    console.log('\n📋 3. 创建新的正确路由');
    
    // 方法1: 使用简单的路由创建，不带复杂的 Schema
    try {
      const simpleRouteData = {
        title: 'Student Dashboard',
        type: 'page'
        // 不提供 schemaUid，让 NocoBase 自动创建
      };

      const simpleRouteResponse = await client.post('/desktopRoutes:create', simpleRouteData);
      const simpleRoute = simpleRouteResponse.data.data;
      
      console.log('   ✅ 创建简单路由成功:');
      console.log(`      - ID: ${simpleRoute.id}`);
      console.log(`      - 标题: "${simpleRoute.title}"`);
      console.log(`      - Schema UID: ${simpleRoute.schemaUid || 'auto-generated'}`);
      console.log(`      - 类型: ${simpleRoute.type}`);
      
    } catch (error) {
      console.log(`   ❌ 创建简单路由失败: ${error.response?.data?.message || error.message}`);
    }

    // 方法2: 创建一个组路由，然后在其下创建页面
    try {
      console.log('\n   创建组路由和子页面:');
      
      // 创建组路由
      const groupRouteData = {
        title: 'Management',
        type: 'group'
      };

      const groupRouteResponse = await client.post('/desktopRoutes:create', groupRouteData);
      const groupRoute = groupRouteResponse.data.data;
      
      console.log(`   ✅ 创建组路由成功: ID ${groupRoute.id}`);

      // 在组下创建页面路由
      const pageRouteData = {
        title: 'Student List',
        type: 'page',
        parentId: groupRoute.id
      };

      const pageRouteResponse = await client.post('/desktopRoutes:create', pageRouteData);
      const pageRoute = pageRouteResponse.data.data;
      
      console.log(`   ✅ 创建子页面路由成功: ID ${pageRoute.id}`);
      console.log(`      - 标题: "${pageRoute.title}"`);
      console.log(`      - Schema UID: ${pageRoute.schemaUid || 'auto-generated'}`);
      console.log(`      - 父级 ID: ${pageRoute.parentId}`);
      
    } catch (error) {
      console.log(`   ❌ 创建组路由失败: ${error.response?.data?.message || error.message}`);
    }

    // 4. 验证最终结果
    console.log('\n📋 4. 验证最终结果');
    
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    
    console.log(`   ✅ 当前有 ${finalRoutes.length} 个路由:`);
    
    // 只显示主要的路由（非 tabs 类型）
    const mainRoutes = finalRoutes.filter(route => route.type !== 'tabs');
    mainRoutes.forEach(route => {
      const status = route.schemaUid ? '✅' : '⚠️';
      console.log(`      ${status} ID: ${route.id}, "${route.title}", 类型: ${route.type}, Schema: ${route.schemaUid || 'null'}`);
    });

    // 检查是否还有问题路由
    const remainingProblems = finalRoutes.filter(route => 
      (route.title === 'null' || route.title === null) && route.type !== 'tabs'
    );
    
    if (remainingProblems.length === 0) {
      console.log('\n   🎉 所有主要路由都正常！');
    } else {
      console.log(`\n   ⚠️ 还有 ${remainingProblems.length} 个问题路由需要处理`);
    }

    console.log('\n🎯 路由重建完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 现在应该有正常工作的路由了！');

  } catch (error) {
    console.error('❌ 重建失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行重建
recreateCorrectRoute().catch(console.error);
