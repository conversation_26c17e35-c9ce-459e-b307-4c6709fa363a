import axios from 'axios';
import testConfig from './test-config.js';

// 创建完全正确的页面结构 - 完全复制手动创建页面的结构
async function createCorrectPageStructure() {
  console.log('🔧 创建完全正确的页面结构\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    const manualPageUid = '2mk30f1pasa';
    
    console.log('📋 策略:');
    console.log('   1. 获取手动创建页面的完整结构');
    console.log('   2. 完全复制这个结构到我们的页面');
    console.log('   3. 只修改必要的 UID 和名称');
    
    // 1. 获取手动创建页面的完整结构
    console.log('\n📋 1. 获取手动创建页面的完整结构');
    const manualResponse = await client.get(`/uiSchemas:getJsonSchema/${manualPageUid}`);
    const manualSchema = manualResponse.data.data;
    
    console.log('📊 手动创建页面的完整结构:');
    console.log(JSON.stringify(manualSchema, null, 2));
    
    // 2. 获取当前页面状态
    console.log('\n📋 2. 获取当前页面状态');
    const currentResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = currentResponse.data.data;
    
    console.log('📊 当前页面结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 3. 创建完全复制的结构
    console.log('\n📋 3. 创建完全复制的结构');
    
    // 完全复制手动页面的结构，只修改 UID 和 name
    const correctStructure = {
      ...manualSchema,
      'x-uid': pageUid,  // 使用我们页面的 UID
      name: currentSchema.name || manualSchema.name  // 保持当前的 name 或使用手动页面的
    };
    
    console.log('📊 正确的页面结构:');
    console.log(JSON.stringify(correctStructure, null, 2));
    
    // 4. 应用新结构
    console.log('\n📋 4. 应用新结构');
    
    try {
      const patchResponse = await client.post('/uiSchemas:patch', {
        'x-uid': pageUid,
        ...correctStructure
      });
      
      console.log('✅ 页面结构更新成功!');
      console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ 更新页面结构失败:', error.response?.status, error.response?.data || error.message);
      
      // 如果 patch 失败，尝试使用 remove + insert 的方式
      console.log('\n🔄 尝试替代方案: remove + insert');
      
      try {
        // 先删除现有的页面
        await client.post('/uiSchemas:remove', {
          'x-uid': pageUid
        });
        console.log('✅ 删除现有页面成功');
        
        // 然后插入新的结构
        await client.post('/uiSchemas:insert', {
          values: correctStructure
        });
        console.log('✅ 插入新页面结构成功');
        
      } catch (replaceError) {
        console.log('❌ 替代方案也失败:', replaceError.response?.status, replaceError.response?.data || replaceError.message);
        return;
      }
    }
    
    // 5. 验证结果
    console.log('\n📋 5. 验证结果');
    
    try {
      const verifyResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const verifiedSchema = verifyResponse.data.data;
      
      console.log('📊 更新后的页面结构:');
      console.log(JSON.stringify(verifiedSchema, null, 2));
      
      // 检查结构是否与手动页面一致
      const structureMatches = (
        verifiedSchema.type === manualSchema.type &&
        verifiedSchema['x-component'] === manualSchema['x-component'] &&
        verifiedSchema['x-uid'] === pageUid &&
        verifiedSchema['x-async'] === manualSchema['x-async'] &&
        !verifiedSchema.schema // 不应该有嵌套的 schema 属性
      );
      
      console.log('\n🔍 结构验证:');
      console.log(`   - 类型匹配: ${verifiedSchema.type === manualSchema.type} (${verifiedSchema.type} vs ${manualSchema.type})`);
      console.log(`   - 组件匹配: ${verifiedSchema['x-component'] === manualSchema['x-component']} (${verifiedSchema['x-component']} vs ${manualSchema['x-component']})`);
      console.log(`   - UID正确: ${verifiedSchema['x-uid'] === pageUid}`);
      console.log(`   - 异步匹配: ${verifiedSchema['x-async'] === manualSchema['x-async']}`);
      console.log(`   - 无嵌套schema: ${!verifiedSchema.schema}`);
      console.log(`   - 整体结构匹配: ${structureMatches}`);
      
      if (structureMatches) {
        console.log('🎉 页面结构完全匹配手动创建的页面！');
        console.log('💡 现在页面应该显示 "Add block" 按钮了');
      } else {
        console.log('⚠️ 页面结构还有差异，需要进一步调整');
        
        // 显示具体差异
        console.log('\n🔍 具体差异:');
        Object.keys(manualSchema).forEach(key => {
          if (key !== 'x-uid' && key !== 'name') {
            const manualValue = manualSchema[key];
            const verifiedValue = verifiedSchema[key];
            if (JSON.stringify(manualValue) !== JSON.stringify(verifiedValue)) {
              console.log(`   - ${key}: 手动="${JSON.stringify(manualValue)}" vs 当前="${JSON.stringify(verifiedValue)}"`);
            }
          }
        });
      }
      
    } catch (error) {
      console.log('❌ 验证失败:', error.response?.status, error.response?.data || error.message);
    }
    
    console.log('\n🎯 修复完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageUid}`);
    console.log('\n💡 验证步骤:');
    console.log('   1. 刷新页面');
    console.log('   2. 确认页面在编辑模式下');
    console.log('   3. 查看是否显示 "Add block" 按钮');
    console.log('   4. 对比与手动创建的"123"页面的行为');
    
    console.log('\n🚀 如果成功，我们就完全解决了页面结构问题！');

  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行创建
createCorrectPageStructure().catch(console.error);
