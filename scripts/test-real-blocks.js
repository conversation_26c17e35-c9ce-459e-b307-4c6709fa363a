import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            console.log('📥 Raw output:', line);
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    console.log('📥 Response:', JSON.stringify(response, null, 2));
    
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 Sending ${method} request:`, JSON.stringify(request, null, 2));
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

// 从响应文本中提取 JSON 数据
function extractJsonFromResponse(text) {
  try {
    // 尝试直接解析
    return JSON.parse(text);
  } catch (e) {
    // 尝试从文本中提取 JSON
    const jsonMatch = text.match(/(\[[\s\S]*\]|\{[\s\S]*\})/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1]);
      } catch (e2) {
        console.warn('Failed to parse JSON from response:', text.substring(0, 200));
        return null;
      }
    }
    return null;
  }
}

async function testRealBlockOperations() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 在真实页面上添加区块测试 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    });

    // 2. 获取路由列表，找到一个页面
    console.log('\n📋 Step 2: Get routes to find a page');
    const routesResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: { tree: true }
    });

    // 解析路由响应
    let routes = null;
    if (routesResult && routesResult.content && routesResult.content[0]) {
      const text = routesResult.content[0].text;
      const match = text.match(/Found \d+ routes:\s*(\[[\s\S]*\])/);
      if (match) {
        routes = JSON.parse(match[1]);
        console.log(`✅ Found ${routes.length} routes`);
      }
    }

    if (!routes || routes.length === 0) {
      throw new Error('No routes found');
    }

    // 找到第一个页面类型的路由
    let targetPage = null;
    function findPageRoute(routeList) {
      for (const route of routeList) {
        if (route.type === 'page' && route.schemaUid) {
          return route;
        }
        if (route.children && route.children.length > 0) {
          const found = findPageRoute(route.children);
          if (found) return found;
        }
      }
      return null;
    }

    targetPage = findPageRoute(routes);
    
    if (!targetPage) {
      console.log('⚠️ No page routes found, creating a new page...');
      
      // 创建一个新页面用于测试
      const createPageResult = await mcpClient.sendRequest('tools/call', {
        name: 'create_page_route',
        arguments: {
          title: '区块测试页面',
          template: 'blank',
          icon: 'FileOutlined'
        }
      });
      
      // 从创建响应中提取页面信息
      if (createPageResult && createPageResult.content && createPageResult.content[0]) {
        const text = createPageResult.content[0].text;
        const match = text.match(/created successfully:\s*(\{[\s\S]*\})/);
        if (match) {
          const pageData = JSON.parse(match[1]);
          targetPage = pageData;
          console.log(`✅ Created new page: ${pageData.title} (UID: ${pageData.schemaUid})`);
        }
      }
    } else {
      console.log(`✅ Found target page: ${targetPage.title} (UID: ${targetPage.schemaUid})`);
    }

    if (!targetPage || !targetPage.schemaUid) {
      throw new Error('Could not find or create a suitable page');
    }

    // 3. 获取页面的 Schema 结构
    console.log('\n📋 Step 3: Get page schema');
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: targetPage.schemaUid }
    });

    // 从 schema 中找到 Grid 容器的 UID
    let gridUid = null;
    if (schemaResult && schemaResult.content && schemaResult.content[0]) {
      const text = schemaResult.content[0].text;
      const match = text.match(/Page schema retrieved successfully:\s*(\{[\s\S]*\})/);
      if (match) {
        const schema = JSON.parse(match[1]);
        
        // 递归查找 Grid 组件
        function findGrid(obj) {
          if (obj && typeof obj === 'object') {
            if (obj['x-component'] === 'Grid') {
              return obj['x-uid'];
            }
            if (obj.properties) {
              for (const key in obj.properties) {
                const result = findGrid(obj.properties[key]);
                if (result) return result;
              }
            }
          }
          return null;
        }
        
        gridUid = findGrid(schema);
        console.log(`✅ Found Grid container UID: ${gridUid}`);
      }
    }

    if (!gridUid) {
      throw new Error('Could not find Grid container in page schema');
    }

    // 4. 获取集合列表
    console.log('\n📋 Step 4: Get collections for data blocks');
    const collectionsResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });

    let collections = [];
    if (collectionsResult && collectionsResult.content && collectionsResult.content[0]) {
      const text = collectionsResult.content[0].text;
      const match = text.match(/Found (\d+) collections:/);
      if (match) {
        // 简单提取集合名称
        const collectionMatches = text.match(/• (\w+) \(/g);
        if (collectionMatches) {
          collections = collectionMatches.map(m => m.replace(/• (\w+) \(/, '$1'));
        }
      }
    }

    console.log(`✅ Found collections: ${collections.join(', ')}`);

    // 5. 开始添加各种区块
    console.log('\n📋 Step 5: Adding various blocks to the page');

    // 5.1 添加 Markdown 区块
    console.log('\n🔸 Adding Markdown block...');
    await mcpClient.sendRequest('tools/call', {
      name: 'add_markdown_block',
      arguments: {
        parentUid: gridUid,
        title: '欢迎使用 NocoBase 区块系统',
        content: `# 🎉 NocoBase 区块系统演示

这是一个通过 **MCP 工具** 动态创建的 Markdown 区块！

## 功能特性

- ✅ 支持多种区块类型
- ✅ 动态创建和管理
- ✅ 灵活的配置选项
- ✅ 完整的生命周期管理

## 测试时间
${new Date().toLocaleString('zh-CN')}

---
*本区块由 NocoBase MCP 服务器自动生成*`
      }
    });

    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 5.2 如果有集合，添加表格区块
    if (collections.length > 0) {
      console.log('\n🔸 Adding Table block...');
      await mcpClient.sendRequest('tools/call', {
        name: 'add_table_block',
        arguments: {
          parentUid: gridUid,
          collectionName: collections[0],
          title: `${collections[0]} 数据表格`,
          position: 'beforeEnd'
        }
      });

      await new Promise(resolve => setTimeout(resolve, 1000));

      // 5.3 添加表单区块
      console.log('\n🔸 Adding Form block...');
      await mcpClient.sendRequest('tools/call', {
        name: 'add_form_block',
        arguments: {
          parentUid: gridUid,
          collectionName: collections[0],
          title: `创建 ${collections[0]} 记录`,
          type: 'create',
          position: 'beforeEnd'
        }
      });

      await new Promise(resolve => setTimeout(resolve, 1000));

      // 5.4 添加详情区块
      console.log('\n🔸 Adding Details block...');
      await mcpClient.sendRequest('tools/call', {
        name: 'add_details_block',
        arguments: {
          parentUid: gridUid,
          collectionName: collections[0],
          title: `${collections[0]} 详情信息`,
          position: 'beforeEnd'
        }
      });
    }

    // 6. 查看最终的页面区块结构
    console.log('\n📋 Step 6: List all blocks in the page');
    await mcpClient.sendRequest('tools/call', {
      name: 'list_page_blocks',
      arguments: { schemaUid: targetPage.schemaUid }
    });

    console.log('\n🎉 区块添加完成！');
    console.log(`\n📱 请访问以下页面查看效果：`);
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${targetPage.path || targetPage.name}`);
    console.log(`\n📝 页面信息：`);
    console.log(`   - 页面标题: ${targetPage.title}`);
    console.log(`   - Schema UID: ${targetPage.schemaUid}`);
    console.log(`   - Grid UID: ${gridUid}`);
    console.log(`   - 使用的集合: ${collections.join(', ')}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动测试
testRealBlockOperations().catch(console.error);
