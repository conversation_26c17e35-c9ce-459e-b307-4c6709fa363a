#!/usr/bin/env node

// 1) 找到最新的页面（或标题以 MCP 测试页面 开头）
// 2) 打开标题栏（enableHeader/displayTitle）
// 3) 给页面插入一个 Markdown 区块
// 4) 列出页面区块确认

import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let pick = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // dump all routes
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); const list = payload?.data || payload || [];
          const pages = []; (function walk(nodes=[]) { for (const n of nodes) { if (n.type === 'page') pages.push(n); if (Array.isArray(n.children)) walk(n.children); } })(list);
          // 先找标题以 MCP 测试页面 开头，否则取最新创建的页面
          const mcpPages = pages.filter(p => typeof p.title === 'string' && p.title.startsWith('MCP 测试页面'));
          const arr = mcpPages.length ? mcpPages : pages;
          if (!arr.length) { console.log('未找到任何页面'); server.kill(); return; }
          arr.sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          pick = arr[0];
          console.log('选中页面:', { id: pick.id, title: pick.title, schemaUid: pick.schemaUid });
          // 开启标题栏
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'update_route', arguments: { id: pick.id, enableHeader: true, displayTitle: true } } });
        } else if (msg.id === 3) {
          console.log('已更新标题栏设置, 开始获取页面 Schema');
          // 获取页面 schema
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: pick.schemaUid } } });
        } else if (msg.id === 4) {
          console.log('准备插入 Markdown 区块');
          // 插入 markdown，parentUid 传 page schemaUid，insertBlockToGrid 内部会追加 .grid
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'add_markdown_block', arguments: { parentUid: pick.schemaUid, title: '通过 MCP 添加的 Markdown', content: '# 由 MCP 添加\n\n这个区块用于验证编辑模式。' } } });
        } else if (msg.id === 5) {
          console.log('已插入 Markdown，列出页面区块');
          send(server, { jsonrpc: '2.0', id: 6, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: pick.schemaUid } } });
        } else if (msg.id === 6) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          console.log(raw);
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'fix-latest-mcp-page', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });

