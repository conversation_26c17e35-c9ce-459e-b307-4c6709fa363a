import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 测试简单的 collection 创建
async function testSimpleCollection() {
  console.log('🧪 测试简单的 Collection 创建\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 创建一个简单的 collection
    console.log('📋 1. 创建简单的 collection');
    
    const simpleCollectionData = {
      name: 'simple_notes',
      title: 'Simple Notes',
      description: 'A simple notes collection'
    };

    try {
      const createResponse = await client.post('/collections:create', simpleCollectionData);
      console.log('   ✅ 创建 simple_notes collection 成功');
      console.log('   响应数据:', JSON.stringify(createResponse.data, null, 2));
    } catch (error) {
      console.log(`   ❌ 创建 simple_notes collection 失败: ${error.response?.data?.message || error.message}`);
      if (error.response?.data) {
        console.log('   错误详情:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // 2. 列出所有 collections 验证
    console.log('\n📋 2. 验证 collection 创建结果');
    
    const collectionsResponse = await client.get('/collections:list');
    const collections = collectionsResponse.data.data;
    console.log(`   ✅ 当前有 ${collections.length} 个 collections:`);
    collections.forEach(collection => {
      console.log(`      - ${collection.name}: "${collection.title || 'N/A'}"`);
    });

    // 3. 如果创建成功，尝试添加字段
    const simpleNotesCollection = collections.find(c => c.name === 'simple_notes');
    if (simpleNotesCollection) {
      console.log('\n📋 3. 为 simple_notes 添加字段');
      
      // 添加标题字段
      try {
        const titleFieldData = {
          name: 'title',
          type: 'string',
          title: 'Note Title',
          required: true
        };
        
        await client.post('/collections:setField', {
          collectionName: 'simple_notes',
          ...titleFieldData
        });
        console.log('   ✅ 添加 title 字段成功');
      } catch (error) {
        console.log(`   ❌ 添加 title 字段失败: ${error.response?.data?.message || error.message}`);
      }

      // 添加内容字段
      try {
        const contentFieldData = {
          name: 'content',
          type: 'text',
          title: 'Note Content'
        };
        
        await client.post('/collections:setField', {
          collectionName: 'simple_notes',
          ...contentFieldData
        });
        console.log('   ✅ 添加 content 字段成功');
      } catch (error) {
        console.log(`   ❌ 添加 content 字段失败: ${error.response?.data?.message || error.message}`);
      }

      // 4. 创建一些测试记录
      console.log('\n📋 4. 创建测试记录');
      
      const testNotes = [
        {
          title: 'First Note',
          content: 'This is my first note using MCP NocoBase.'
        },
        {
          title: 'Second Note',
          content: 'Testing the record creation functionality.'
        }
      ];

      for (const note of testNotes) {
        try {
          await client.post('/simple_notes:create', note);
          console.log(`   ✅ 创建记录成功: ${note.title}`);
        } catch (error) {
          console.log(`   ❌ 创建记录失败 (${note.title}): ${error.response?.data?.message || error.message}`);
        }
      }

      // 5. 查询记录验证
      console.log('\n📋 5. 查询记录验证');
      
      try {
        const notesResponse = await client.get('/simple_notes:list');
        const notes = notesResponse.data.data;
        console.log(`   ✅ 找到 ${notes.length} 条记录:`);
        notes.forEach(note => {
          console.log(`      - ID: ${note.id}, 标题: "${note.title}"`);
        });
      } catch (error) {
        console.log(`   ❌ 查询记录失败: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎯 简单 Collection 测试完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行简单测试
testSimpleCollection().catch(console.error);
