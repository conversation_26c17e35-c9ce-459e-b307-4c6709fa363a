#!/usr/bin/env node

// 对比 page_1 和我创建的页面的完整路由配置
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let step = 0;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          step = 1;
          console.log('=== 获取 page_1 的详细路由配置 ===');
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'get_route', arguments: { id: 1 } } });
        } 
        else if (msg.id === 2 && step === 1) {
          console.log('page_1 详细配置:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 2;
          console.log('\n=== 获取 page_1 的 tabs 子路由配置 ===');
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'get_route', arguments: { id: 2 } } });
        }
        else if (msg.id === 3 && step === 2) {
          console.log('page_1 tabs 子路由详细配置:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 3;
          console.log('\n=== 获取我创建的页面配置 ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_route', arguments: { id: 13 } } });
        }
        else if (msg.id === 4 && step === 3) {
          console.log('我创建的页面详细配置:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 4;
          console.log('\n=== 获取我创建的页面的 tabs 子路由配置 ===');
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'get_route', arguments: { id: 14 } } });
        }
        else if (msg.id === 5 && step === 4) {
          console.log('我创建的页面 tabs 子路由详细配置:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 关键差异分析 ===');
          console.log('请对比以上配置，找出关键差异：');
          console.log('1. 路由类型 (type)');
          console.log('2. 菜单配置 (menu 相关)');
          console.log('3. 权限配置 (permissions 相关)');
          console.log('4. 显示配置 (hidden, sort 等)');
          console.log('5. 其他特殊配置');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'compare-routes', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
