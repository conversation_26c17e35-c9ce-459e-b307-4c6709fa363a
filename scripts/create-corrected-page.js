#!/usr/bin/env node

// 创建一个使用正确 "tabs" 类型的页面
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let mainPageId = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          console.log('=== 创建使用正确 tabs 类型的页面 ===');
          send(server, { 
            jsonrpc: '2.0', 
            id: 2, 
            method: 'tools/call', 
            params: { 
              name: 'create_page_route', 
              arguments: { 
                title: `正确的页面 ${Date.now()}`,
                template: 'blank',
                enableTabs: false,
                icon: 'ExperimentOutlined'
              } 
            } 
          });
        } 
        else if (msg.id === 2) {
          console.log('主页面创建结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          const text = content.find(item => item.type === 'text')?.text || '';
          const match = text.match(/"id":\s*(\d+)/);
          if (match) {
            mainPageId = parseInt(match[1]);
            console.log(`\n=== 为页面 ${mainPageId} 创建 tabs 子路由（使用正确的 "tabs" 类型）===`);
            
            send(server, {
              jsonrpc: '2.0',
              id: 3,
              method: 'tools/call',
              params: {
                name: 'create_tabs_route',
                arguments: {
                  parentId: mainPageId,
                  tabSchemaName: `corrected_tab_${Date.now()}`
                }
              }
            });
          } else {
            console.log('无法解析主页面 ID');
            server.kill();
          }
        }
        else if (msg.id === 3) {
          console.log('\ntabs 子路由创建结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 验证新页面结构 ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'routes_tree_overview', arguments: { includeIds: true } } });
        }
        else if (msg.id === 4) {
          console.log('更新后的路由树:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n✅ 页面创建完成！');
          console.log('🔍 请测试新创建的页面是否能看到"添加区块"按钮');
          console.log('📝 关键修正：子路由类型从 "tab" 改为 "tabs"');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'create-corrected', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
