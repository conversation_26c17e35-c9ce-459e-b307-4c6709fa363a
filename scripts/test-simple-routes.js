import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let buffer = '';
  server.stdout.on('data', (data) => {
    buffer += data.toString();
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';
    
    lines.forEach(line => {
      if (line.trim()) {
        try {
          const response = JSON.parse(line);
          console.log('📥 Response:', JSON.stringify(response, null, 2));
        } catch (e) {
          console.log('📥 Raw output:', line);
        }
      }
    });
  });

  server.stderr.on('data', (data) => {
    console.log('🔍 Server log:', data.toString());
  });

  return server;
}

function sendRequest(server, method, params = {}) {
  const request = {
    jsonrpc: '2.0',
    id: requestId++,
    method,
    ...(Object.keys(params).length > 0 && { params })
  };

  console.log(`📤 Sending ${method} request:`, JSON.stringify(request, null, 2));
  server.stdin.write(JSON.stringify(request) + '\n');
}

async function testBasicRoutes() {
  const server = startMCPServer();
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('\n=== 基础路由测试 ===\n');

  // 1. 初始化
  console.log('📋 Step 1: Initialize');
  sendRequest(server, 'initialize', {
    protocolVersion: '2024-11-05',
    capabilities: {},
    clientInfo: { name: 'test-client', version: '1.0.0' }
  });

  await new Promise(resolve => setTimeout(resolve, 1000));

  // 2. 列出工具
  console.log('📋 Step 2: List tools');
  sendRequest(server, 'tools/list');

  await new Promise(resolve => setTimeout(resolve, 1000));

  // 3. 列出现有路由
  console.log('📋 Step 3: List existing routes');
  sendRequest(server, 'tools/call', {
    name: 'list_routes',
    arguments: { tree: true }
  });

  await new Promise(resolve => setTimeout(resolve, 2000));

  // 4. 创建一个简单的分组
  console.log('📋 Step 4: Create a simple group');
  sendRequest(server, 'tools/call', {
    name: 'create_group_route',
    arguments: {
      title: '测试分组',
      icon: 'FolderOutlined'
    }
  });

  await new Promise(resolve => setTimeout(resolve, 2000));

  // 5. 再次列出路由查看变化
  console.log('📋 Step 5: List routes after creation');
  sendRequest(server, 'tools/call', {
    name: 'list_routes',
    arguments: { tree: true }
  });

  await new Promise(resolve => setTimeout(resolve, 2000));

  // 6. 关闭服务器
  console.log('\n✅ 测试完成，关闭服务器');
  server.kill();
}

// 启动测试
testBasicRoutes().catch(console.error);
