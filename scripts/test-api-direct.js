import testConfig from './test-config.js';

async function testAPIDirectly() {
  console.log('🔍 Testing NocoBase API directly...');
  console.log('Base URL:', testConfig.baseUrl);
  console.log('App:', testConfig.app);
  
  try {
    // 测试基本连接
    const response = await fetch(`${testConfig.baseUrl}/desktopRoutes:listAccessible?tree=true`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${testConfig.token}`,
        'X-App': testConfig.app,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API response:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ API error:', errorText);
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
}

testAPIDirectly();
