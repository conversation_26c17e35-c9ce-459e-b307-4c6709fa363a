#!/usr/bin/env node

// 检查镇街集合的完整字段信息
import { NocoBaseClient } from './dist/client.js';

async function checkTownsFields() {
  console.log('🔍 检查镇街集合的完整字段信息...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 获取集合详细信息，包括所有字段
    console.log('📋 获取镇街集合详细信息');
    const collection = await client.getCollection('towns');
    console.log(`集合名称: ${collection.name}`);
    console.log(`集合标题: ${collection.title}`);
    console.log(`自动生成ID: ${collection.autoGenId}`);
    console.log(`创建时间字段: ${collection.createdAt}`);
    console.log(`更新时间字段: ${collection.updatedAt}`);
    console.log(`创建人字段: ${collection.createdBy}`);
    console.log(`更新人字段: ${collection.updatedBy}`);
    console.log();

    // 获取所有字段详细信息
    console.log('📋 所有字段详细信息:');
    const fields = await client.listFields('towns');
    console.log(`总共 ${fields.length} 个字段:\n`);
    
    fields.forEach((field, index) => {
      console.log(`${index + 1}. 字段名: ${field.name}`);
      console.log(`   类型: ${field.type}`);
      console.log(`   接口: ${field.interface || '无'}`);
      console.log(`   主键: ${field.primaryKey || false}`);
      console.log(`   自增: ${field.autoIncrement || false}`);
      console.log(`   允许空值: ${field.allowNull !== false}`);
      console.log(`   UI标题: ${field.uiSchema?.title || '无标题'}`);
      console.log(`   UI组件: ${field.uiSchema?.['x-component'] || '无组件'}`);
      console.log('');
    });

    // 检查一条记录的完整数据
    console.log('📋 检查记录的完整数据:');
    const records = await client.listRecords('towns', { pageSize: 1 });
    if (records.data.length > 0) {
      const record = records.data[0];
      console.log('第一条记录的所有字段:');
      Object.keys(record).forEach(key => {
        console.log(`   ${key}: ${record[key]}`);
      });
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
  }
}

checkTownsFields().catch(console.error);
