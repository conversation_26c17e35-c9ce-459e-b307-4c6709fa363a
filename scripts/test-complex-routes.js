import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// 路由管理器 - 跟踪创建的路由和它们的ID
class RouteManager {
  constructor() {
    this.routes = new Map(); // id -> route info
    this.routesByName = new Map(); // name -> id
    this.routesByType = new Map(); // type -> [ids]
    this.routeTree = []; // 路由树结构
  }

  addRoute(name, routeData) {
    const id = routeData.id;
    this.routes.set(id, { ...routeData, name });
    this.routesByName.set(name, id);

    const type = routeData.type || 'unknown';
    if (!this.routesByType.has(type)) {
      this.routesByType.set(type, []);
    }
    this.routesByType.get(type).push(id);

    console.log(`✅ Route registered: ${name} (ID: ${id}, Type: ${type})`);
  }

  getRouteId(name) {
    return this.routesByName.get(name);
  }

  getRoute(id) {
    return this.routes.get(id);
  }

  getRoutesByType(type) {
    return this.routesByType.get(type) || [];
  }

  getAllRoutes() {
    return Array.from(this.routes.values());
  }

  updateRouteTree(treeData) {
    this.routeTree = treeData;
  }

  clear() {
    this.routes.clear();
    this.routesByName.clear();
    this.routesByType.clear();
    this.routeTree = [];
    console.log('🧹 Route manager cleared');
  }

  printSummary() {
    console.log('\n📊 Route Manager Summary:');
    console.log(`Total routes: ${this.routes.size}`);
    for (const [type, ids] of this.routesByType) {
      console.log(`  ${type}: ${ids.length}`);
    }
  }
}

// MCP 客户端 - 处理与服务器的通信
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map(); // requestId -> { resolve, reject, timestamp }
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            console.log('📥 Raw output:', line);
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    console.log('📥 Response:', JSON.stringify(response, null, 2));

    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);

      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 10000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 Sending ${method} request:`, JSON.stringify(request, null, 2));

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });

      // 设置超时
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);

      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

// 测试套件 - 管理和执行所有测试
class TestSuite {
  constructor() {
    this.routeManager = new RouteManager();
    this.mcpClient = null;
    this.testResults = [];
    this.server = null;
  }

  async initialize() {
    this.server = startMCPServer();
    this.mcpClient = new MCPClient(this.server);

    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 测试服务器是否响应
    try {
      console.log('🔍 Testing server connection...');
      await this.mcpClient.sendRequest('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: { name: 'test-client', version: '1.0.0' }
      }, 5000);
      console.log('✅ Server connection established');
    } catch (error) {
      console.warn('⚠️ Server connection test failed:', error.message);
      // 继续执行，可能服务器需要更多时间
    }

    console.log('✅ Server initialization completed');
  }

  async runTest(testName, testFn) {
    console.log(`\n🧪 Running test: ${testName}`);
    const startTime = Date.now();

    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.testResults.push({ name: testName, status: 'PASSED', duration });
      console.log(`✅ Test passed: ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.testResults.push({ name: testName, status: 'FAILED', duration, error: error.message });
      console.log(`❌ Test failed: ${testName} (${duration}ms) - ${error.message}`);
    }
  }

  async createRouteWithTracking(type, name, params) {
    const toolName = `create_${type}_route`;
    const result = await this.mcpClient.sendRequest('tools/call', {
      name: toolName,
      arguments: params
    });

    // 解析响应中的路由信息
    if (result && result.content && result.content[0] && result.content[0].text) {
      try {
        const text = result.content[0].text;
        const match = text.match(/created successfully:\s*(\{[\s\S]*\})/);
        if (match) {
          const routeData = JSON.parse(match[1]);
          this.routeManager.addRoute(name, routeData);
          return routeData;
        }
      } catch (e) {
        console.warn(`⚠️ Failed to parse route data for ${name}:`, e.message);
      }
    }

    return result;
  }

  async verifyRouteStructure() {
    console.log('🔍 Verifying route structure...');
    const result = await this.mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: { tree: true }
    });

    if (result && result.content && result.content[0] && result.content[0].text) {
      try {
        const text = result.content[0].text;
        const match = text.match(/Found \d+ routes:\s*(\[[\s\S]*\])/);
        if (match) {
          const routes = JSON.parse(match[1]);
          this.routeManager.updateRouteTree(routes);
          console.log(`✅ Found ${routes.length} routes in structure`);
          return routes;
        }
      } catch (e) {
        console.warn('⚠️ Failed to parse route structure:', e.message);
      }
    }

    return [];
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    const allRoutes = this.routeManager.getAllRoutes();

    // 按创建顺序倒序删除（先删除子路由）
    const sortedRoutes = allRoutes.sort((a, b) => (b.id || 0) - (a.id || 0));

    for (const route of sortedRoutes) {
      try {
        await this.mcpClient.sendRequest('tools/call', {
          name: 'delete_route',
          arguments: { id: route.id }
        });
        console.log(`🗑️ Deleted route: ${route.name} (ID: ${route.id})`);
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.warn(`⚠️ Failed to delete route ${route.name}:`, error.message);
      }
    }

    this.routeManager.clear();
  }

  generateReport() {
    console.log('\n📊 Test Report');
    console.log('='.repeat(50));

    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const total = this.testResults.length;

    console.log(`Total tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success rate: ${total > 0 ? ((passed / total) * 100).toFixed(1) : 0}%`);

    if (failed > 0) {
      console.log('\n❌ Failed tests:');
      this.testResults
        .filter(r => r.status === 'FAILED')
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
    }

    console.log('\n⏱️ Performance:');
    this.testResults.forEach(r => {
      console.log(`  ${r.name}: ${r.duration}ms`);
    });

    this.routeManager.printSummary();
  }

  async shutdown() {
    if (this.server) {
      console.log('\n🔌 Shutting down server...');
      this.server.kill();
    }
  }
}

async function testComplexRouteOperations() {
  const testSuite = new TestSuite();

  try {
    await testSuite.initialize();
    console.log('\n=== 复杂路由操作测试套件 ===\n');

    // 测试1：基础路由创建和跟踪
    await testSuite.runTest('Basic Route Creation', async () => {
      // 创建基础路由结构
      await testSuite.createRouteWithTracking('group', 'main-group', {
        title: '主分组',
        icon: 'FolderOutlined'
      });

      await testSuite.createRouteWithTracking('group', 'sub-group', {
        title: '子分组',
        icon: 'AppstoreOutlined'
      });

      await testSuite.createRouteWithTracking('page', 'page-1', {
        title: '页面1',
        template: 'blank',
        icon: 'FileOutlined'
      });

      await testSuite.createRouteWithTracking('page', 'page-2', {
        title: '页面2',
        template: 'table',
        icon: 'TableOutlined'
      });

      await testSuite.createRouteWithTracking('link', 'external-link', {
        title: '外部链接',
        href: 'https://example.com',
        icon: 'LinkOutlined'
      });

      // 验证创建的路由
      const routes = await testSuite.verifyRouteStructure();
      if (routes.length === 0) {
        throw new Error('No routes found after creation');
      }
    });

    // 测试2：复杂嵌套结构
    await testSuite.runTest('Complex Nested Structure', async () => {
      const mainGroupId = testSuite.routeManager.getRouteId('main-group');
      const subGroupId = testSuite.routeManager.getRouteId('sub-group');

      if (!mainGroupId || !subGroupId) {
        throw new Error('Required parent routes not found');
      }

      // 创建嵌套页面
      await testSuite.createRouteWithTracking('page', 'nested-page-1', {
        title: '嵌套页面1',
        parentId: mainGroupId,
        template: 'dashboard',
        icon: 'DashboardOutlined'
      });

      await testSuite.createRouteWithTracking('page', 'nested-page-2', {
        title: '嵌套页面2',
        parentId: subGroupId,
        template: 'table',
        icon: 'TableOutlined'
      });

      // 创建深层嵌套
      await testSuite.createRouteWithTracking('group', 'deep-group', {
        title: '深层分组',
        parentId: mainGroupId,
        icon: 'FolderOpenOutlined'
      });

      const deepGroupId = testSuite.routeManager.getRouteId('deep-group');
      if (deepGroupId) {
        await testSuite.createRouteWithTracking('page', 'deep-page', {
          title: '深层页面',
          parentId: deepGroupId,
          template: 'blank',
          icon: 'FileTextOutlined'
        });
      }

      await testSuite.verifyRouteStructure();
    });

    // 测试3：路由移动操作
    await testSuite.runTest('Route Movement Operations', async () => {
      const page1Id = testSuite.routeManager.getRouteId('page-1');
      const page2Id = testSuite.routeManager.getRouteId('page-2');
      const mainGroupId = testSuite.routeManager.getRouteId('main-group');
      const subGroupId = testSuite.routeManager.getRouteId('sub-group');

      if (!page1Id || !page2Id || !mainGroupId || !subGroupId) {
        throw new Error('Required routes not found for movement test');
      }

      // 移动页面1到主分组后面
      await testSuite.mcpClient.sendRequest('tools/call', {
        name: 'move_route',
        arguments: {
          sourceId: page1Id,
          targetId: mainGroupId,
          method: 'insertAfter'
        }
      });

      // 移动页面2到子分组内部
      await testSuite.mcpClient.sendRequest('tools/call', {
        name: 'move_route',
        arguments: {
          sourceId: page2Id,
          targetId: subGroupId,
          method: 'prepend'
        }
      });

      await testSuite.verifyRouteStructure();
    });

    // 测试4：批量操作
    await testSuite.runTest('Batch Operations', async () => {
      // 批量创建多个路由
      const batchRoutes = [];
      for (let i = 1; i <= 5; i++) {
        const routeName = `batch-page-${i}`;
        await testSuite.createRouteWithTracking('page', routeName, {
          title: `批量页面${i}`,
          template: i % 2 === 0 ? 'table' : 'blank',
          icon: 'FileOutlined'
        });
        batchRoutes.push(routeName);
      }

      // 验证批量创建的路由
      for (const routeName of batchRoutes) {
        const routeId = testSuite.routeManager.getRouteId(routeName);
        if (!routeId) {
          throw new Error(`Batch route ${routeName} not found`);
        }
      }

      await testSuite.verifyRouteStructure();
    });

    // 测试5：错误处理
    await testSuite.runTest('Error Handling', async () => {
      // 尝试移动不存在的路由
      try {
        await testSuite.mcpClient.sendRequest('tools/call', {
          name: 'move_route',
          arguments: {
            sourceId: 99999,
            targetId: 1,
            method: 'insertAfter'
          }
        });
        // 如果没有抛出错误，则测试失败
        throw new Error('Expected error for invalid route ID, but operation succeeded');
      } catch (error) {
        // 预期的错误，测试通过
        console.log('✅ Correctly handled invalid route ID error');
      }

      // 尝试删除不存在的路由
      try {
        await testSuite.mcpClient.sendRequest('tools/call', {
          name: 'delete_route',
          arguments: { id: 99999 }
        });
      } catch (error) {
        console.log('✅ Correctly handled delete non-existent route error');
      }
    });

    // 测试6：性能测试
    await testSuite.runTest('Performance Test', async () => {
      const startTime = Date.now();
      const performanceRoutes = [];

      // 快速创建多个路由
      for (let i = 1; i <= 10; i++) {
        const routeName = `perf-route-${i}`;
        await testSuite.createRouteWithTracking('page', routeName, {
          title: `性能测试路由${i}`,
          template: 'blank',
          icon: 'FileOutlined'
        });
        performanceRoutes.push(routeName);
      }

      const creationTime = Date.now() - startTime;
      console.log(`⏱️ Created 10 routes in ${creationTime}ms`);

      // 测试列表性能
      const listStartTime = Date.now();
      await testSuite.verifyRouteStructure();
      const listTime = Date.now() - listStartTime;
      console.log(`⏱️ Listed routes in ${listTime}ms`);

      if (creationTime > 30000) { // 30秒超时
        throw new Error(`Route creation too slow: ${creationTime}ms`);
      }
    });

    // 最终清理
    await testSuite.cleanup();

  } catch (error) {
    console.error('❌ Test suite failed:', error);
    await testSuite.cleanup();
  } finally {
    testSuite.generateReport();
    await testSuite.shutdown();
  }
}

// 启动测试
testComplexRouteOperations().catch(console.error);