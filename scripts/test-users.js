#!/usr/bin/env node

/**
 * 测试 NocoBase Users API 功能
 * 
 * 使用方法：
 * node scripts/test-users.js
 */

import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testUsersAPI() {
  console.log('🚀 Starting NocoBase Users API Test...\n');

  // 创建 MCP 客户端
  const transport = new StdioClientTransport({
    command: 'node',
    args: [
      'dist/index.js',
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ]
  });

  const mcpClient = new Client({
    name: 'test-users-client',
    version: '1.0.0'
  });

  let testResults = {
    passed: 0,
    failed: 0,
    errors: []
  };

  function logTest(name, success, error = null) {
    if (success) {
      console.log(`✅ ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ ${name}: ${error}`);
      testResults.failed++;
      testResults.errors.push({ test: name, error });
    }
  }

  try {
    await mcpClient.connect(transport);
    
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== NocoBase Users API 测试 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    try {
      await mcpClient.initialize();
      logTest('Initialize MCP client', true);
    } catch (error) {
      logTest('Initialize MCP client', false, error.message);
    }

    // 2. 列出工具
    console.log('\n📋 Step 2: List tools');
    try {
      const tools = await mcpClient.listTools();
      const userTools = tools.tools?.filter(t => t.name.includes('user')) || [];
      console.log('User-related tools:', userTools.map(t => t.name).join(', '));
      logTest('List user tools', userTools.length > 0, userTools.length === 0 ? 'No user tools found' : null);
    } catch (error) {
      logTest('List user tools', false, error.message);
    }

    // 3. 列出用户
    console.log('\n📋 Step 3: List users');
    try {
      const result = await mcpClient.callTool({
        name: 'list_users',
        arguments: {
          page: 1,
          pageSize: 5
        }
      });
      console.log('List users result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('List users', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('List users', false, error.message);
    }

    // 4. 获取特定用户（假设用户ID为1）
    console.log('\n📋 Step 4: Get user by ID');
    try {
      const result = await mcpClient.callTool({
        name: 'get_user',
        arguments: {
          id: 1
        }
      });
      console.log('Get user result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Get user by ID', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Get user by ID', false, error.message);
    }

    // 5. 创建测试用户
    console.log('\n📋 Step 5: Create test user');
    try {
      const result = await mcpClient.callTool({
        name: 'create_user',
        arguments: {
          nickname: 'Test User',
          username: 'testuser_' + Date.now(),
          email: 'testuser_' + Date.now() + '@example.com',
          password: 'testpassword123'
        }
      });
      console.log('Create user result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Create test user', !result.isError, result.isError ? result.content?.[0]?.text : null);
      
      // 如果创建成功，尝试解析用户ID用于后续测试
      if (!result.isError) {
        try {
          const userText = result.content?.[0]?.text || '';
          const userMatch = userText.match(/"id":\s*(\d+)/);
          if (userMatch) {
            const newUserId = userMatch[1];
            console.log(`Created user with ID: ${newUserId}`);
            
            // 6. 更新用户
            console.log('\n📋 Step 6: Update user');
            try {
              const updateResult = await mcpClient.callTool({
                name: 'update_user',
                arguments: {
                  id: newUserId,
                  nickname: 'Updated Test User'
                }
              });
              console.log('Update user result:', updateResult.content?.[0]?.text?.substring(0, 200) + '...');
              logTest('Update user', !updateResult.isError, updateResult.isError ? updateResult.content?.[0]?.text : null);
            } catch (error) {
              logTest('Update user', false, error.message);
            }

            // 7. 删除测试用户
            console.log('\n📋 Step 7: Delete test user');
            try {
              const deleteResult = await mcpClient.callTool({
                name: 'delete_user',
                arguments: {
                  id: newUserId
                }
              });
              console.log('Delete user result:', deleteResult.content?.[0]?.text);
              logTest('Delete test user', !deleteResult.isError, deleteResult.isError ? deleteResult.content?.[0]?.text : null);
            } catch (error) {
              logTest('Delete test user', false, error.message);
            }
          }
        } catch (parseError) {
          console.log('Could not parse created user ID for further testing');
        }
      }
    } catch (error) {
      logTest('Create test user', false, error.message);
    }

    // 8. 测试过滤功能
    console.log('\n📋 Step 8: Test user filtering');
    try {
      const result = await mcpClient.callTool({
        name: 'list_users',
        arguments: {
          filter: { "id": 1 },
          appends: ["roles"]
        }
      });
      console.log('Filter users result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Filter users', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Filter users', false, error.message);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    testResults.failed++;
    testResults.errors.push({ test: 'Overall test', error: error.message });
  } finally {
    // 清理
    try {
      await mcpClient.close();
    } catch (e) {
      // 忽略关闭错误
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Results:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.errors.length > 0) {
      console.log('\n🔍 Error Details:');
      testResults.errors.forEach(({ test, error }) => {
        console.log(`  - ${test}: ${error}`);
      });
    }
    
    console.log('\n🎯 Users API tools are now available for NocoBase MCP server!');
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// 运行测试
testUsersAPI().catch(console.error);
