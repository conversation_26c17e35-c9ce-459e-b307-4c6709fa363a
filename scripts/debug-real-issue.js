import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`\n📤 REQUEST: ${method}`);
    console.log('📋 Params:', JSON.stringify(params, null, 2));
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function debugRealIssue() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🔍 深度调试：为什么区块没有添加到页面 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'debug-client', version: '1.0.0' }
    });

    // 2. 首先检查路由，确认页面 UID
    console.log('\n📋 Step 2: Check routes to verify page UID');
    const routesResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: { tree: true }
    });

    console.log('📥 ROUTES RESPONSE:');
    if (routesResult && routesResult.content && routesResult.content[0]) {
      console.log(routesResult.content[0].text);
    }

    // 3. 获取页面 Schema - 详细调试
    const pageUid = 'page-1754235700602-luqwmsxu9';
    console.log(`\n📋 Step 3: Get page schema - DETAILED DEBUG`);
    console.log(`🎯 Target page UID: ${pageUid}`);
    
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: pageUid }
    });

    console.log('\n📥 SCHEMA RESPONSE:');
    if (schemaResult && schemaResult.content && schemaResult.content[0]) {
      const responseText = schemaResult.content[0].text;
      console.log('📄 Full response text:');
      console.log(responseText);
      
      // 尝试解析 JSON
      const match = responseText.match(/Page schema retrieved successfully:\s*(\{[\s\S]*\})/);
      if (match) {
        try {
          const schema = JSON.parse(match[1]);
          console.log('\n📊 Parsed schema structure:');
          console.log(JSON.stringify(schema, null, 2));
          
          // 分析 Schema 结构
          console.log('\n🔍 Schema analysis:');
          console.log('- Schema type:', schema.type);
          console.log('- Schema component:', schema['x-component']);
          console.log('- Schema UID:', schema['x-uid']);
          console.log('- Has properties:', !!schema.properties);
          
          if (schema.properties) {
            console.log('- Properties keys:', Object.keys(schema.properties));
            
            // 查找所有组件
            function analyzeComponents(obj, path = '') {
              if (obj && typeof obj === 'object') {
                if (obj['x-component']) {
                  console.log(`  ${path}: ${obj['x-component']} (UID: ${obj['x-uid'] || 'none'})`);
                }
                if (obj.properties) {
                  for (const key in obj.properties) {
                    analyzeComponents(obj.properties[key], `${path}.${key}`);
                  }
                }
              }
            }
            
            console.log('\n🧩 All components in schema:');
            analyzeComponents(schema, 'root');
          }
        } catch (e) {
          console.log('❌ Failed to parse schema JSON:', e.message);
          console.log('📄 Raw JSON text:');
          console.log(match[1].substring(0, 500) + '...');
        }
      } else {
        console.log('❌ No schema JSON found in response');
      }
    } else {
      console.log('❌ No schema response received');
    }

    // 4. 尝试直接调用 NocoBase API
    console.log('\n📋 Step 4: Test direct NocoBase API call');
    
    // 检查我们的 NocoBaseClient 是否正确工作
    console.log('🔧 Testing NocoBaseClient directly...');
    
    // 这里我们需要检查我们的 client.js 实现
    console.log('⚠️ Need to check if our NocoBaseClient.getPageSchema() method is working correctly');

    // 5. 尝试一个简单的 Markdown 区块添加，并详细记录过程
    console.log('\n📋 Step 5: Attempt to add a simple block with full debugging');
    
    try {
      const addResult = await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: pageUid, // 直接使用页面 UID
          title: '🔍 调试测试区块',
          content: '# 这是一个调试测试区块\n\n如果您看到这个区块，说明添加成功了！',
          position: 'beforeEnd'
        }
      });
      
      console.log('\n📥 ADD BLOCK RESPONSE:');
      console.log(JSON.stringify(addResult, null, 2));
      
    } catch (error) {
      console.log('\n❌ ADD BLOCK ERROR:');
      console.log('Error message:', error.message);
      console.log('Error stack:', error.stack);
    }

    // 6. 检查区块是否真的被添加
    console.log('\n📋 Step 6: Verify if block was actually added');
    
    try {
      const listResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: pageUid }
      });
      
      console.log('\n📥 LIST BLOCKS RESPONSE:');
      console.log(JSON.stringify(listResult, null, 2));
      
    } catch (error) {
      console.log('\n❌ LIST BLOCKS ERROR:');
      console.log('Error message:', error.message);
    }

    // 7. 总结调试结果
    console.log('\n📋 Step 7: Debug Summary');
    console.log('\n🔍 调试完成！请检查以上输出来找出问题所在：');
    console.log('1. 页面 Schema 是否正确获取？');
    console.log('2. 区块添加 API 调用是否成功？');
    console.log('3. 是否有错误信息？');
    console.log('4. NocoBase API 响应是什么？');

  } catch (error) {
    console.error('\n❌ Debug failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动调试
debugRealIssue().catch(console.error);
