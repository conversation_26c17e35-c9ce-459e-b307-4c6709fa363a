#!/usr/bin/env node

// 分析 page_1 在原始数据中的完整配置
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          console.log('=== 获取原始路由数据 ===');
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } 
        else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); 
          const allRoutes = payload?.data || payload || [];
          
          console.log('=== 查找 page_1 和我创建的页面 ===');
          
          function findAndCompare(nodes = [], depth = 0) {
            for (const n of nodes) {
              if (n.title === 'page_1') {
                console.log('\n=== page_1 完整配置 ===');
                console.log(JSON.stringify(n, null, 2));
                
                if (n.children && n.children.length > 0) {
                  console.log('\n=== page_1 的子路由配置 ===');
                  for (const child of n.children) {
                    console.log(JSON.stringify(child, null, 2));
                  }
                }
              }
              
              if (n.title && n.title.includes('page_1 副本')) {
                console.log('\n=== 我创建的页面完整配置 ===');
                console.log(JSON.stringify(n, null, 2));
                
                if (n.children && n.children.length > 0) {
                  console.log('\n=== 我创建的页面的子路由配置 ===');
                  for (const child of n.children) {
                    console.log(JSON.stringify(child, null, 2));
                  }
                }
              }
              
              if (Array.isArray(n.children)) {
                findAndCompare(n.children, depth + 1);
              }
            }
          }
          
          findAndCompare(allRoutes);
          
          console.log('\n=== 关键差异分析 ===');
          console.log('对比以上两个配置，重点关注：');
          console.log('1. sort 字段');
          console.log('2. options 字段');
          console.log('3. menuSchemaUid 字段');
          console.log('4. 权限相关字段');
          console.log('5. 其他可能影响编辑功能的字段');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'analyze-raw', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
