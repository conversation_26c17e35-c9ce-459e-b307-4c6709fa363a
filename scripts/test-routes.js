#!/usr/bin/env node

// 简单的测试脚本来验证路由工具
import { spawn } from 'child_process';

const config = {
  baseUrl: 'https://n.astra.xin',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInRlbXAiOnRydWUsImlhdCI6MTc1NDIzNDUwNiwic2lnbkluVGltZSI6MTc1NDIzNDUwNjk2MCwiZXhwIjoxNzU0MzIwOTA2LCJqdGkiOiIyYWE2MTg2NC05MzBhLTRiYmUtYTQzYy1mOTE5ZGEzZGNkMDQifQ.JdiegyE62VL5zg1refeK5MxIL6KI6AjJBELGWmovSm0',
  app: 'mcp_playground'
};

async function testMCPServer() {
  console.log('🚀 Starting MCP Server test...');
  
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', config.baseUrl,
    '--token', config.token,
    '--app', config.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  // 发送 MCP 初始化请求
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };

  console.log('📤 Sending initialize request...');
  server.stdin.write(JSON.stringify(initRequest) + '\n');

  // 等待响应
  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      try {
        const response = JSON.parse(line);
        console.log('📥 Received response:', JSON.stringify(response, null, 2));
        
        if (response.id === 1) {
          // 初始化成功，测试工具列表
          testToolsList(server);
        } else if (response.id === 2) {
          // 工具列表响应，测试创建路由
          testCreateRoute(server);
        } else if (response.id === 3) {
          // 创建路由响应，测试列出路由
          testListRoutes(server);
        } else if (response.id === 4) {
          // 列出路由响应，结束测试
          console.log('✅ All tests completed successfully!');
          server.kill();
        }
      } catch (error) {
        console.log('📄 Raw output:', line);
      }
    });
  });

  server.stderr.on('data', (data) => {
    console.log('🔍 Server log:', data.toString());
  });

  server.on('close', (code) => {
    console.log(`🏁 Server exited with code ${code}`);
  });
}

function testToolsList(server) {
  const toolsRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list'
  };

  console.log('📤 Sending tools/list request...');
  server.stdin.write(JSON.stringify(toolsRequest) + '\n');
}

function testCreateRoute(server) {
  const createRouteRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'create_page_route',
      arguments: {
        title: 'MCP 测试页面',
        template: 'blank',
        icon: 'ExperimentOutlined'
      }
    }
  };

  console.log('📤 Sending create_page_route request...');
  server.stdin.write(JSON.stringify(createRouteRequest) + '\n');
}

function testListRoutes(server) {
  const listRoutesRequest = {
    jsonrpc: '2.0',
    id: 4,
    method: 'tools/call',
    params: {
      name: 'list_routes',
      arguments: {
        tree: true
      }
    }
  };

  console.log('📤 Sending list_routes request...');
  server.stdin.write(JSON.stringify(listRoutesRequest) + '\n');
}

testMCPServer().catch(console.error);
