import axios from 'axios';
import testConfig from './test-config.js';

// 分析正确创建的页面结构
async function analyzeCorrectPage() {
  console.log('🔍 分析正确创建的页面结构\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 页面信息
    const correctPageUid = 'cafh7yoyd6w';  // 刚刚手工创建的"MCP 测试页面"
    const manualPageUid = '2mk30f1pasa';   // 之前手工创建的"123"页面
    
    console.log('📋 分析页面信息:');
    console.log(`   - 新创建页面: ${correctPageUid} ("MCP 测试页面")`);
    console.log(`   - 对比页面: ${manualPageUid} ("123"页面)`);
    
    // 1. 获取新创建页面的Schema
    console.log('\n=== 获取新创建页面的Schema ===');
    let correctSchema = null;
    try {
      const correctResponse = await client.get(`/uiSchemas:getJsonSchema/${correctPageUid}`);
      correctSchema = correctResponse.data.data;
      console.log('✅ 新创建页面Schema获取成功');
    } catch (error) {
      console.log('❌ 获取新创建页面Schema失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    // 2. 获取对比页面的Schema
    console.log('\n=== 获取对比页面的Schema ===');
    let manualSchema = null;
    try {
      const manualResponse = await client.get(`/uiSchemas:getJsonSchema/${manualPageUid}`);
      manualSchema = manualResponse.data.data;
      console.log('✅ 对比页面Schema获取成功');
    } catch (error) {
      console.log('❌ 获取对比页面Schema失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    // 3. 显示完整Schema结构
    console.log('\n=== 完整Schema结构 ===');
    
    console.log('\n🔸 新创建页面完整Schema:');
    console.log(JSON.stringify(correctSchema, null, 2));
    
    console.log('\n🔸 对比页面完整Schema:');
    console.log(JSON.stringify(manualSchema, null, 2));

    // 4. 分析关键差异
    console.log('\n=== 关键差异分析 ===');
    
    // 对比根级结构
    console.log('\n📊 根级结构对比:');
    console.log(`   新页面类型: ${correctSchema.type}`);
    console.log(`   新页面组件: ${correctSchema['x-component']}`);
    console.log(`   新页面UID: ${correctSchema['x-uid']}`);
    console.log(`   新页面异步: ${correctSchema['x-async']}`);
    
    console.log(`   对比页面类型: ${manualSchema.type}`);
    console.log(`   对比页面组件: ${manualSchema['x-component']}`);
    console.log(`   对比页面UID: ${manualSchema['x-uid']}`);
    console.log(`   对比页面异步: ${manualSchema['x-async']}`);
    
    // 检查是否有嵌套schema
    const hasNestedSchema = !!correctSchema.schema;
    console.log(`\n🔍 新页面有嵌套schema: ${hasNestedSchema}`);
    
    if (hasNestedSchema) {
      console.log('⚠️ 新页面有嵌套schema结构，这可能不是我们期望的');
      console.log('📊 嵌套schema结构:');
      console.log(JSON.stringify(correctSchema.schema, null, 2));
    } else {
      console.log('✅ 新页面是简单的Page组件结构，这是正确的');
    }
    
    // 5. 获取路由信息
    console.log('\n=== 路由信息分析 ===');
    
    try {
      const routesResponse = await client.get('/desktopRoutes:list');
      const routes = routesResponse.data.data;
      
      const correctRoute = routes.find(route => route.schemaUid === correctPageUid);
      const manualRoute = routes.find(route => route.schemaUid === manualPageUid);
      
      if (correctRoute) {
        console.log('\n🔸 新创建页面路由信息:');
        console.log(`   - ID: ${correctRoute.id}`);
        console.log(`   - 标题: "${correctRoute.title}"`);
        console.log(`   - Schema UID: ${correctRoute.schemaUid}`);
        console.log(`   - 类型: ${correctRoute.type}`);
        console.log(`   - 路径: ${correctRoute.path}`);
      }
      
      if (manualRoute) {
        console.log('\n🔸 对比页面路由信息:');
        console.log(`   - ID: ${manualRoute.id}`);
        console.log(`   - 标题: "${manualRoute.title}"`);
        console.log(`   - Schema UID: ${manualRoute.schemaUid}`);
        console.log(`   - 类型: ${manualRoute.type}`);
        console.log(`   - 路径: ${manualRoute.path}`);
      }
      
    } catch (error) {
      console.log('❌ 获取路由信息失败:', error.response?.data || error.message);
    }
    
    // 6. 总结关键发现
    console.log('\n=== 关键发现总结 ===');
    
    console.log('\n🎯 成功的页面创建流程:');
    console.log('1. ✅ 使用 NocoBase 的官方界面创建页面');
    console.log('2. ✅ 页面自动获得正确的结构');
    console.log('3. ✅ 自动显示 "Add block" 按钮');
    console.log('4. ✅ 页面可以正常添加区块');
    
    console.log('\n🔍 正确的页面特征:');
    if (!hasNestedSchema) {
      console.log('- ✅ 简单的Page组件结构（无嵌套schema）');
    } else {
      console.log('- ⚠️ 有嵌套schema结构');
    }
    console.log(`- ✅ 类型: ${correctSchema.type}`);
    console.log(`- ✅ 组件: ${correctSchema['x-component']}`);
    console.log(`- ✅ 异步: ${correctSchema['x-async']}`);
    
    console.log('\n🚀 下一步行动:');
    console.log('1. 🔍 研究 NocoBase 的页面创建 API');
    console.log('2. 🛠️ 修复 MCP 工具的页面创建逻辑');
    console.log('3. 🧪 测试在正确的页面上添加区块');
    console.log('4. ✅ 验证完整的工作流程');

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行分析
analyzeCorrectPage().catch(console.error);
