import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 ${method}: ${params.name || method}`);
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function testFixedBlocks() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🔧 测试修复后的区块添加功能 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'fixed-test-client', version: '1.0.0' }
    });

    // 2. 添加一个简单的 Markdown 区块
    const pageUid = 'page-1754235700602-luqwmsxu9';
    console.log(`\n📋 Step 2: Add Markdown block to page: ${pageUid}`);
    
    try {
      const result = await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: pageUid,
          title: '🎉 修复测试成功！',
          content: `# 🚀 NocoBase MCP 区块系统修复成功！

## ✅ 修复的问题

1. **区块模板缺少必要属性** - 添加了 \`name\` 和 \`x-uid\` 属性
2. **Grid 容器路径错误** - 使用正确的 \`pageUid.grid\` 路径
3. **API 调用方式优化** - 创建了专门的 \`insertBlockToGrid\` 方法

## 🎯 测试结果

如果您看到这个区块，说明：
- ✅ MCP 工具正常工作
- ✅ 区块模板正确生成
- ✅ API 调用成功执行
- ✅ 区块成功添加到页面

## 📅 测试信息

- **测试时间**: ${new Date().toLocaleString('zh-CN')}
- **页面 UID**: ${pageUid}
- **区块类型**: Markdown
- **状态**: 🎊 成功！

---
*通过 NocoBase MCP 服务器自动生成和修复*`
        }
      });
      
      console.log('✅ Markdown block added successfully!');
      console.log('📥 Response:', JSON.stringify(result, null, 2));
      
    } catch (error) {
      console.log('❌ Failed to add Markdown block:');
      console.log('Error:', error.message);
      
      // 如果还是失败，让我们尝试更详细的调试
      console.log('\n🔍 Additional debugging...');
      
      // 检查页面 Schema
      try {
        const schemaResult = await mcpClient.sendRequest('tools/call', {
          name: 'get_page_schema',
          arguments: { schemaUid: pageUid }
        });
        
        console.log('📄 Page schema check:');
        if (schemaResult && schemaResult.content && schemaResult.content[0]) {
          const text = schemaResult.content[0].text;
          console.log(text.substring(0, 300) + '...');
        }
      } catch (schemaError) {
        console.log('❌ Schema check failed:', schemaError.message);
      }
    }

    // 3. 如果 Markdown 成功，尝试添加数据区块
    console.log('\n📋 Step 3: Try adding a data block');
    
    try {
      const collectionsResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_collections',
        arguments: {}
      });

      let collections = [];
      if (collectionsResult && collectionsResult.content && collectionsResult.content[0]) {
        const text = collectionsResult.content[0].text;
        const collectionMatches = text.match(/• (\w+) \(/g);
        if (collectionMatches) {
          collections = collectionMatches.map(m => m.replace(/• (\w+) \(/, '$1'));
        }
      }

      if (collections.length > 0) {
        console.log(`🔸 Adding table block for: ${collections[0]}`);
        
        const tableResult = await mcpClient.sendRequest('tools/call', {
          name: 'add_table_block',
          arguments: {
            parentUid: pageUid,
            collectionName: collections[0],
            title: `📊 ${collections[0]} 数据表格（修复测试）`
          }
        });
        
        console.log('✅ Table block added successfully!');
        console.log('📥 Response:', JSON.stringify(tableResult, null, 2));
      }
      
    } catch (error) {
      console.log('❌ Failed to add table block:', error.message);
    }

    // 4. 验证结果
    console.log('\n📋 Step 4: Verify results');
    
    try {
      const listResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: pageUid }
      });
      
      console.log('📋 Current blocks in page:');
      if (listResult && listResult.content && listResult.content[0]) {
        console.log(listResult.content[0].text);
      }
      
    } catch (error) {
      console.log('⚠️ Could not list blocks:', error.message);
    }

    // 5. 最终结果
    console.log('\n🎊 测试完成！');
    console.log('\n📱 请刷新页面查看结果：');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/page-1754235700602-luqwmsxu9`);
    
    console.log('\n💡 如果区块出现了，说明修复成功！');
    console.log('   如果还是空白，我们需要进一步调试 NocoBase API 的具体要求。');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动测试
testFixedBlocks().catch(console.error);
