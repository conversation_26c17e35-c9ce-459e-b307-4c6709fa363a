#!/usr/bin/env node

/**
 * 测试菜单创建功能与响应格式化器
 * 创建菜单结构：
 * g1 (菜单组)
 * ├─ p1 (页面)
 * └─ g1-g1.1 (子菜单组)
 *    └─ g1-g1.1-p2 (页面)
 */

import { NocoBaseClient } from '../dist/client.js';
import { handleCreateMenuGroup, handleCreateMenuPage } from '../dist/tools/menu-operations.js';
import { uid } from '../dist/utils.js';

// 使用测试环境配置
const client = new NocoBaseClient({
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
  app: 'mcp_playground'
});

async function createMenuStructure() {
  console.log('🏗️  开始创建菜单结构测试\n');
  console.log('=' .repeat(80));
  console.log('目标结构:');
  console.log('g1 (菜单组)');
  console.log('├─ p1 (页面)');
  console.log('└─ g1-g1.1 (子菜单组)');
  console.log('   └─ g1-g1.1-p2 (页面)');
  console.log('=' .repeat(80));

  const createdItems = [];

  try {
    // 步骤 1: 创建主菜单组 g1
    console.log('\n📁 步骤 1: 创建主菜单组 "g1"');
    console.log('-'.repeat(50));
    
    const g1Response = await handleCreateMenuGroup(client, {
      title: 'g1',
      icon: 'FolderOutlined',
      parentId: null
    });
    
    console.log('✅ 主菜单组创建响应:');
    console.log(g1Response.content[0].text);
    
    // 从响应中提取 g1 的 ID
    const g1Data = g1Response.content[0].text.match(/"id":\s*(\d+)/);
    const g1Id = g1Data ? parseInt(g1Data[1]) : null;
    
    if (!g1Id) {
      throw new Error('无法获取 g1 的 ID');
    }
    
    createdItems.push({ name: 'g1', id: g1Id, type: 'group' });
    console.log(`📋 记录: g1 ID = ${g1Id}`);

    console.log('\n' + '='.repeat(80));

    // 步骤 2: 在 g1 下创建页面 p1
    console.log('\n📄 步骤 2: 在 g1 下创建页面 "p1"');
    console.log('-'.repeat(50));
    
    const p1SchemaUid = uid();
    const p1Response = await handleCreateMenuPage(client, {
      title: 'p1',
      icon: 'FileOutlined',
      parentId: g1Id,
      schemaUid: p1SchemaUid,
      enableTabs: false,
      enableHeader: true
    });
    
    console.log('✅ 页面 p1 创建响应:');
    console.log(p1Response.content[0].text);
    
    const p1Data = p1Response.content[0].text.match(/"id":\s*(\d+)/);
    const p1Id = p1Data ? parseInt(p1Data[1]) : null;
    
    if (p1Id) {
      createdItems.push({ name: 'p1', id: p1Id, type: 'page', parentId: g1Id, schemaUid: p1SchemaUid });
      console.log(`📋 记录: p1 ID = ${p1Id}, Schema UID = ${p1SchemaUid}`);
    }

    console.log('\n' + '='.repeat(80));

    // 步骤 3: 在 g1 下创建子菜单组 g1-g1.1
    console.log('\n📁 步骤 3: 在 g1 下创建子菜单组 "g1-g1.1"');
    console.log('-'.repeat(50));
    
    const g1_g1_1Response = await handleCreateMenuGroup(client, {
      title: 'g1-g1.1',
      icon: 'FolderOpenOutlined',
      parentId: g1Id
    });
    
    console.log('✅ 子菜单组创建响应:');
    console.log(g1_g1_1Response.content[0].text);
    
    const g1_g1_1Data = g1_g1_1Response.content[0].text.match(/"id":\s*(\d+)/);
    const g1_g1_1Id = g1_g1_1Data ? parseInt(g1_g1_1Data[1]) : null;
    
    if (!g1_g1_1Id) {
      throw new Error('无法获取 g1-g1.1 的 ID');
    }
    
    createdItems.push({ name: 'g1-g1.1', id: g1_g1_1Id, type: 'group', parentId: g1Id });
    console.log(`📋 记录: g1-g1.1 ID = ${g1_g1_1Id}`);

    console.log('\n' + '='.repeat(80));

    // 步骤 4: 在 g1-g1.1 下创建页面 g1-g1.1-p2
    console.log('\n📄 步骤 4: 在 g1-g1.1 下创建页面 "g1-g1.1-p2"');
    console.log('-'.repeat(50));
    
    const p2SchemaUid = uid();
    const p2Response = await handleCreateMenuPage(client, {
      title: 'g1-g1.1-p2',
      icon: 'FileTextOutlined',
      parentId: g1_g1_1Id,
      schemaUid: p2SchemaUid,
      enableTabs: true,
      enableHeader: true
    });
    
    console.log('✅ 页面 g1-g1.1-p2 创建响应:');
    console.log(p2Response.content[0].text);
    
    const p2Data = p2Response.content[0].text.match(/"id":\s*(\d+)/);
    const p2Id = p2Data ? parseInt(p2Data[1]) : null;
    
    if (p2Id) {
      createdItems.push({ name: 'g1-g1.1-p2', id: p2Id, type: 'page', parentId: g1_g1_1Id, schemaUid: p2SchemaUid });
      console.log(`📋 记录: g1-g1.1-p2 ID = ${p2Id}, Schema UID = ${p2SchemaUid}`);
    }

    console.log('\n' + '='.repeat(80));

    // 总结
    console.log('\n🎉 菜单结构创建完成！');
    console.log('\n📊 创建的菜单项目:');
    createdItems.forEach((item, index) => {
      const indent = item.parentId ? '  └─ ' : '';
      const parentInfo = item.parentId ? ` (父级: ${item.parentId})` : '';
      const schemaInfo = item.schemaUid ? ` [Schema: ${item.schemaUid}]` : '';
      console.log(`${index + 1}. ${indent}${item.name} (ID: ${item.id}, 类型: ${item.type})${parentInfo}${schemaInfo}`);
    });

    console.log('\n✅ 响应格式化器效果总结:');
    console.log('- ✅ 菜单数据自动识别为 "menu" 类型');
    console.log('- ✅ 结构化显示菜单项配置信息');
    console.log('- ✅ 清晰展示父子关系和层级结构');
    console.log('- ✅ 统一的错误处理格式');
    console.log('- ✅ YAML 格式提升可读性');

    console.log('\n🔍 验证建议:');
    console.log('1. 登录 NocoBase 管理界面查看菜单结构');
    console.log('2. 检查菜单层级关系是否正确');
    console.log('3. 验证页面路由是否可访问');
    console.log('4. 确认图标和配置是否生效');

  } catch (error) {
    console.error('\n❌ 菜单创建过程中发生错误:', error.message);
    
    if (createdItems.length > 0) {
      console.log('\n🧹 已创建的菜单项目:');
      createdItems.forEach(item => {
        console.log(`- ${item.name} (ID: ${item.id})`);
      });
      console.log('\n💡 提示: 您可能需要手动清理这些已创建的菜单项目');
    }
  }
}

// 运行测试
createMenuStructure().catch(console.error);
