import axios from 'axios';
import testConfig from './test-config.js';

// 清理现有路由，只保留手工创建的"123"页面
async function cleanupRoutes() {
  console.log('🧹 清理现有路由，只保留手工创建的"123"页面\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 1. 获取所有路由
    console.log('📋 1. 获取所有现有路由');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    console.log(`✅ 找到 ${routes.length} 个路由:`);
    routes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid}`);
    });
    
    // 2. 识别要保留和删除的路由
    console.log('\n📋 2. 识别要保留和删除的路由');
    
    const keepRouteUid = '2mk30f1pasa'; // 手工创建的"123"页面
    const routesToKeep = routes.filter(route => route.schemaUid === keepRouteUid);
    const routesToDelete = routes.filter(route => route.schemaUid !== keepRouteUid);
    
    console.log(`✅ 要保留的路由 (${routesToKeep.length} 个):`);
    routesToKeep.forEach(route => {
      console.log(`   - 保留: ID ${route.id}, "${route.title}" (${route.schemaUid})`);
    });
    
    console.log(`🗑️ 要删除的路由 (${routesToDelete.length} 个):`);
    routesToDelete.forEach(route => {
      console.log(`   - 删除: ID ${route.id}, "${route.title}" (${route.schemaUid})`);
    });
    
    if (routesToDelete.length === 0) {
      console.log('ℹ️ 没有需要删除的路由');
      return;
    }
    
    // 3. 删除不需要的路由
    console.log('\n📋 3. 删除不需要的路由');
    
    for (const route of routesToDelete) {
      try {
        console.log(`🗑️ 删除路由: ID ${route.id}, "${route.title}"`);
        
        // 先删除对应的 UI Schema
        if (route.schemaUid) {
          try {
            await client.post('/uiSchemas:remove', {
              'x-uid': route.schemaUid
            });
            console.log(`   ✅ 删除 UI Schema: ${route.schemaUid}`);
          } catch (schemaError) {
            console.log(`   ⚠️ 删除 UI Schema 失败: ${schemaError.response?.data?.message || schemaError.message}`);
          }
        }
        
        // 然后删除路由
        await client.post(`/desktopRoutes:destroy?filterByTk=${route.id}`);
        console.log(`   ✅ 删除路由成功: ID ${route.id}`);
        
      } catch (error) {
        console.log(`   ❌ 删除路由失败: ID ${route.id}, 错误: ${error.response?.data?.message || error.message}`);
      }
    }
    
    // 4. 验证清理结果
    console.log('\n📋 4. 验证清理结果');
    
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    
    console.log(`✅ 清理后剩余 ${finalRoutes.length} 个路由:`);
    finalRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid}`);
    });
    
    // 验证"123"页面是否还在
    const keepRoute = finalRoutes.find(route => route.schemaUid === keepRouteUid);
    if (keepRoute) {
      console.log(`🎉 手工创建的"123"页面已保留: ID ${keepRoute.id}`);
    } else {
      console.log('⚠️ 手工创建的"123"页面似乎被意外删除了');
    }
    
    console.log('\n🎯 清理完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 现在菜单应该只显示:');
    console.log('   - 手工创建的"123"页面');
    console.log('   - 系统默认的菜单项（如果有的话）');
    console.log('\n🚀 接下来可以从根目录菜单开始重新创建页面！');

  } catch (error) {
    console.error('❌ 清理失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行清理
cleanupRoutes().catch(console.error);
