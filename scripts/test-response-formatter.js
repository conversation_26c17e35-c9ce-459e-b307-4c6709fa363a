#!/usr/bin/env node

/**
 * 测试响应格式化器功能
 * 演示如何将大量 JSON 数据转换为高度压缩的 YAML 格式
 */

import { ResponseFormatter } from '../dist/utils/response-formatter.js';

// 创建一个更大的 Schema 数据来触发压缩
function createLargeSchemaData() {
  const baseSchema = {
    "_isJSONSchemaObject": true,
  "version": "2.0",
  "type": "void",
  "x-component": "Page",
  "x-uid": "cafh7yoyd6w",
  "x-async": false,
  "x-app-version": "1.7.10",
  "properties": {
    "h4rrhkjj6ye": {
      "_isJSONSchemaObject": true,
      "version": "2.0",
      "type": "void",
      "x-component": "Grid",
      "x-initializer": "page:addBlock",
      "x-uid": "tnum5ahw2a7",
      "x-async": false,
      "x-app-version": "1.7.10",
      "properties": {
        "vapq1bj65ee": {
          "_isJSONSchemaObject": true,
          "version": "2.0",
          "type": "void",
          "x-component": "Grid.Row",
          "x-uid": "l50qhvcemqv",
          "x-async": false,
          "x-app-version": "1.7.10",
          "properties": {
            "az11xfyh2js": {
              "_isJSONSchemaObject": true,
              "version": "2.0",
              "type": "void",
              "x-component": "Grid.Col",
              "x-uid": "4dwubkrzcnj",
              "x-async": false,
              "x-app-version": "1.7.10",
              "properties": {
                "table_block": {
                  "_isJSONSchemaObject": true,
                  "version": "2.0",
                  "type": "void",
                  "x-decorator": "TableBlockProvider",
                  "x-decorator-props": {
                    "collection": "users",
                    "dataSource": "main",
                    "action": "list",
                    "params": {
                      "pageSize": 20
                    },
                    "showIndex": true,
                    "dragSort": false
                  },
                  "x-component": "CardItem",
                  "x-uid": "table_users_main",
                  "x-async": false,
                  "x-app-version": "1.7.10",
                  "properties": {
                    "actions": {
                      "_isJSONSchemaObject": true,
                      "version": "2.0",
                      "type": "void",
                      "x-component": "ActionBar",
                      "x-component-props": {
                        "style": {
                          "marginBottom": 16
                        }
                      },
                      "x-uid": "actions_bar",
                      "x-async": false,
                      "x-app-version": "1.7.10",
                      "properties": {
                        "create": {
                          "_isJSONSchemaObject": true,
                          "version": "2.0",
                          "type": "void",
                          "title": "{{t('Add new')}}",
                          "x-action": "create",
                          "x-component": "Action",
                          "x-component-props": {
                            "icon": "PlusOutlined",
                            "openMode": "drawer",
                            "type": "primary"
                          },
                          "x-uid": "create_action",
                          "x-async": false,
                          "x-app-version": "1.7.10"
                        }
                      }
                    },
                    "table": {
                      "_isJSONSchemaObject": true,
                      "version": "2.0",
                      "type": "array",
                      "x-component": "TableV2",
                      "x-component-props": {
                        "rowKey": "id",
                        "rowSelection": {
                          "type": "checkbox"
                        }
                      },
                      "x-uid": "table_component",
                      "x-async": false,
                      "x-app-version": "1.7.10",
                      "properties": {
                        "username": {
                          "_isJSONSchemaObject": true,
                          "version": "2.0",
                          "type": "void",
                          "x-decorator": "TableV2.Column.Decorator",
                          "x-component": "TableV2.Column",
                          "x-uid": "username_column",
                          "x-async": false,
                          "x-app-version": "1.7.10",
                          "properties": {
                            "username": {
                              "_isJSONSchemaObject": true,
                              "version": "2.0",
                              "x-collection-field": "users.username",
                              "x-component": "CollectionField",
                              "x-read-pretty": true,
                              "x-uid": "username_field",
                              "x-async": false,
                              "x-app-version": "1.7.10"
                            }
                          }
                        },
                        "email": {
                          "_isJSONSchemaObject": true,
                          "version": "2.0",
                          "type": "void",
                          "x-decorator": "TableV2.Column.Decorator",
                          "x-component": "TableV2.Column",
                          "x-uid": "email_column",
                          "x-async": false,
                          "x-app-version": "1.7.10",
                          "properties": {
                            "email": {
                              "_isJSONSchemaObject": true,
                              "version": "2.0",
                              "x-collection-field": "users.email",
                              "x-component": "CollectionField",
                              "x-read-pretty": true,
                              "x-uid": "email_field",
                              "x-async": false,
                              "x-app-version": "1.7.10"
                            }
                          }
                        },
                        "actions": {
                          "_isJSONSchemaObject": true,
                          "version": "2.0",
                          "type": "void",
                          "title": "{{t('Actions')}}",
                          "x-component": "TableV2.Column",
                          "x-uid": "actions_column",
                          "x-async": false,
                          "x-app-version": "1.7.10",
                          "properties": {
                            "popup": {
                              "_isJSONSchemaObject": true,
                              "version": "2.0",
                              "type": "void",
                              "title": "{{t('Popup')}}",
                              "x-action": "customize:popup",
                              "x-component": "Action.Link",
                              "x-component-props": {
                                "openMode": "drawer"
                              },
                              "x-uid": "popup_action",
                              "x-async": false,
                              "x-app-version": "1.7.10"
                            },
                            "view": {
                              "_isJSONSchemaObject": true,
                              "version": "2.0",
                              "type": "void",
                              "title": "{{t('View')}}",
                              "x-action": "view",
                              "x-component": "Action.Link",
                              "x-component-props": {
                                "openMode": "drawer"
                              },
                              "x-uid": "view_action",
                              "x-async": false,
                              "x-app-version": "1.7.10"
                            },
                            "edit": {
                              "_isJSONSchemaObject": true,
                              "version": "2.0",
                              "type": "void",
                              "title": "{{t('Edit')}}",
                              "x-action": "update",
                              "x-component": "Action.Link",
                              "x-component-props": {
                                "icon": "EditOutlined",
                                "openMode": "drawer"
                              },
                              "x-uid": "edit_action",
                              "x-async": false,
                              "x-app-version": "1.7.10"
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  };

  // 复制多个相似的区块来增加数据量
  for (let i = 1; i <= 20; i++) {
    const blockKey = `block_${i}_${Math.random().toString(36).substr(2, 11)}`;
    baseSchema.properties[blockKey] = JSON.parse(JSON.stringify(baseSchema.properties.h4rrhkjj6ye));
    baseSchema.properties[blockKey]["x-uid"] = Math.random().toString(36).substr(2, 11);
  }

  return baseSchema;
}

const mockSchemaData = createLargeSchemaData();

// 创建大量列表数据
function createLargeListData() {
  const data = [];
  for (let i = 1; i <= 100; i++) {
    data.push({
      id: i,
      username: `user${i}`,
      email: `user${i}@example.com`,
      firstName: `FirstName${i}`,
      lastName: `LastName${i}`,
      phone: `******-${String(i).padStart(4, '0')}`,
      address: `${i} Main Street, City ${i}, State ${i}, ${String(i).padStart(5, '0')}`,
      department: `Department ${i % 10}`,
      position: `Position ${i % 5}`,
      salary: 50000 + (i * 1000),
      hireDate: `2024-01-${String((i % 28) + 1).padStart(2, '0')}T00:00:00Z`,
      createdAt: `2024-01-${String((i % 28) + 1).padStart(2, '0')}T00:00:00Z`,
      updatedAt: `2024-01-${String((i % 28) + 1).padStart(2, '0')}T00:00:00Z`,
      isActive: i % 2 === 0,
      metadata: {
        lastLogin: `2024-01-${String((i % 28) + 1).padStart(2, '0')}T12:00:00Z`,
        loginCount: i * 10,
        preferences: {
          theme: i % 2 === 0 ? 'dark' : 'light',
          language: i % 3 === 0 ? 'en' : i % 3 === 1 ? 'zh' : 'es',
          notifications: {
            email: true,
            sms: i % 2 === 0,
            push: true
          }
        }
      }
    });
  }

  return {
    data,
    meta: {
      count: 1500,
      page: 1,
      pageSize: 100,
      totalPage: 15,
      hasNext: true
    }
  };
}

const mockListData = createLargeListData();

// 模拟集合数据
const mockCollectionData = [
  {
    name: "users",
    title: "Users",
    description: "System users with authentication and profile information",
    fields: [
      { name: "id", type: "bigInt", interface: "id" },
      { name: "username", type: "string", interface: "input" },
      { name: "email", type: "string", interface: "email" },
      { name: "password", type: "password", interface: "password" },
      { name: "createdAt", type: "date", interface: "datetime" }
    ]
  },
  {
    name: "posts",
    title: "Posts",
    description: "Blog posts and articles",
    fields: [
      { name: "id", type: "bigInt", interface: "id" },
      { name: "title", type: "string", interface: "input" },
      { name: "content", type: "text", interface: "richText" },
      { name: "authorId", type: "bigInt", interface: "m2o" }
    ]
  }
];

async function testResponseFormatter() {
  console.log('🧪 测试响应格式化器功能\n');
  console.log('=' .repeat(80));

  const formatter = new ResponseFormatter();

  // 测试 Schema 数据格式化
  console.log('\n📋 测试 Schema 数据格式化:');
  console.log('-'.repeat(50));
  const schemaResult = formatter.formatResponse(mockSchemaData, 'schema');
  console.log(schemaResult);

  console.log('\n' + '='.repeat(80));

  // 测试列表数据格式化
  console.log('\n📊 测试列表数据格式化:');
  console.log('-'.repeat(50));
  const listResult = formatter.formatResponse(mockListData, 'list');
  console.log(listResult);

  console.log('\n' + '='.repeat(80));

  // 测试集合数据格式化
  console.log('\n🗂️  测试集合数据格式化:');
  console.log('-'.repeat(50));
  const collectionResult = formatter.formatResponse(mockCollectionData, 'collection');
  console.log(collectionResult);

  console.log('\n' + '='.repeat(80));

  // 测试小数据（不压缩）
  console.log('\n🔍 测试小数据（不压缩）:');
  console.log('-'.repeat(50));
  const smallData = { name: "test", value: 123 };
  const smallResult = formatter.formatResponse(smallData);
  console.log(smallResult);

  console.log('\n✅ 测试完成！');
}

// 运行测试
testResponseFormatter().catch(console.error);
