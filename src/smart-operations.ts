/**
 * 智能操作策略：Schema API 作为"眼睛"
 * 
 * 核心理念：在进行任何操作前，先通过 Schema API 查询页面结构，
 * 了解现状，然后基于分析结果进行精准操作。
 */

import { NocoBaseClient } from './client.js';
import { analyzePageStructure, selectBestInsertionPoint } from './block-templates.js';
import type { PageStructureAnalysis } from './block-templates.js';

export interface SmartOperationContext {
  pageSchemaUid: string;
  analysis: PageStructureAnalysis;
  operationType: 'add_block' | 'modify_block' | 'delete_block' | 'move_block';
  targetBlockType?: string;
  targetBlockUid?: string;
}

export interface SmartOperationResult {
  success: boolean;
  message: string;
  context: SmartOperationContext;
  actions: Array<{
    type: string;
    description: string;
    executed: boolean;
    result?: any;
  }>;
}

/**
 * 智能添加区块 - 先分析页面结构，再精准插入
 */
export async function smartAddBlock(
  client: NocoBaseClient,
  pageSchemaUid: string,
  blockConfig: {
    type: 'table' | 'form' | 'details' | 'markdown' | 'kanban';
    collectionName?: string;
    title?: string;
    content?: string;
    preferences?: {
      preferEmptySpace?: boolean;
      preferNewRow?: boolean;
      maxColumnsPerRow?: number;
    };
  }
): Promise<SmartOperationResult> {
  
  const result: SmartOperationResult = {
    success: false,
    message: '',
    context: {
      pageSchemaUid,
      analysis: {} as PageStructureAnalysis,
      operationType: 'add_block',
      targetBlockType: blockConfig.type
    },
    actions: []
  };
  
  try {
    // 第一步：通过 Schema API "看清"页面结构
    result.actions.push({
      type: 'analyze_structure',
      description: `Analyzing page structure for ${pageSchemaUid}`,
      executed: false
    });
    
    const analysis = await analyzePageStructure(client, pageSchemaUid);
    result.context.analysis = analysis;
    if (result.actions[0]) {
      result.actions[0].executed = true;
      result.actions[0].result = {
      hasGrid: analysis.hasGrid,
      rowCount: analysis.rows.length,
      totalBlocks: analysis.rows.reduce((sum, row) => 
        sum + row.cols.reduce((colSum, col) => colSum + col.blocks.length, 0), 0
      ),
      insertionPoints: analysis.availableInsertionPoints.length
      };
    }

    // 第二步：基于分析结果选择最佳插入位置
    result.actions.push({
      type: 'select_insertion_point',
      description: `Selecting best insertion point for ${blockConfig.type} block`,
      executed: false
    });

    const insertionPoint = selectBestInsertionPoint(
      analysis,
      blockConfig.type,
      blockConfig.preferences
    );
    if (result.actions[1]) {
      result.actions[1].executed = true;
      result.actions[1].result = insertionPoint;
    }
    
    // 第三步：准备区块 Schema
    result.actions.push({
      type: 'prepare_block_schema',
      description: `Preparing ${blockConfig.type} block schema`,
      executed: false
    });
    
    const blockSchema = await prepareBlockSchema(blockConfig);
    if (result.actions[2]) {
      result.actions[2].executed = true;
      result.actions[2].result = { schemaKeys: Object.keys(blockSchema) };
    }

    // 第四步：执行插入操作
    result.actions.push({
      type: 'insert_block',
      description: `Inserting block at ${insertionPoint.uid}`,
      executed: false
    });

    const insertResult = await client.insertBlockSchema(
      insertionPoint.uid,
      blockSchema,
      insertionPoint.position
    );
    if (result.actions[3]) {
      result.actions[3].executed = true;
      result.actions[3].result = insertResult;
    }
    
    // 第五步：验证操作结果
    result.actions.push({
      type: 'verify_result',
      description: 'Verifying block insertion',
      executed: false
    });
    
    const postAnalysis = await analyzePageStructure(client, pageSchemaUid);
    const newBlockCount = postAnalysis.rows.reduce((sum, row) => 
      sum + row.cols.reduce((colSum, col) => colSum + col.blocks.length, 0), 0
    );
    const originalBlockCount = analysis.rows.reduce((sum, row) => 
      sum + row.cols.reduce((colSum, col) => colSum + col.blocks.length, 0), 0
    );
    
    const verified = newBlockCount > originalBlockCount;
    if (result.actions[4]) {
      result.actions[4].executed = true;
      result.actions[4].result = {
        verified,
        originalBlocks: originalBlockCount,
        newBlocks: newBlockCount,
        difference: newBlockCount - originalBlockCount
      };
    }
    
    result.success = verified;
    result.message = verified 
      ? `✅ Successfully added ${blockConfig.type} block. ${insertionPoint.description}`
      : `❌ Block insertion may have failed. Expected block count increase not detected.`;
    
  } catch (error) {
    result.success = false;
    result.message = `❌ Smart block addition failed: ${error instanceof Error ? error.message : String(error)}`;
  }
  
  return result;
}

/**
 * 智能修改区块 - 先定位目标区块，再精准修改
 */
export async function smartModifyBlock(
  client: NocoBaseClient,
  pageSchemaUid: string,
  targetBlockUid: string,
  modifications: {
    title?: string;
    collection?: string;
    componentProps?: any;
    decoratorProps?: any;
  }
): Promise<SmartOperationResult> {
  
  const result: SmartOperationResult = {
    success: false,
    message: '',
    context: {
      pageSchemaUid,
      analysis: {} as PageStructureAnalysis,
      operationType: 'modify_block',
      targetBlockUid
    },
    actions: []
  };
  
  try {
    // 第一步：分析页面结构，定位目标区块
    result.actions.push({
      type: 'locate_target_block',
      description: `Locating target block ${targetBlockUid}`,
      executed: false
    });
    
    const analysis = await analyzePageStructure(client, pageSchemaUid);
    result.context.analysis = analysis;
    
    // 查找目标区块
    let targetBlock = null;
    for (const row of analysis.rows) {
      for (const col of row.cols) {
        targetBlock = col.blocks.find(block => block.uid === targetBlockUid);
        if (targetBlock) break;
      }
      if (targetBlock) break;
    }
    
    if (!targetBlock) {
      throw new Error(`Target block ${targetBlockUid} not found in page structure`);
    }
    
    if (result.actions[0]) {
      result.actions[0].executed = true;
      result.actions[0].result = {
        found: true,
        blockType: targetBlock.type,
        component: targetBlock.component,
        collection: targetBlock.collection
      };
    }
    
    // 第二步：执行修改操作
    result.actions.push({
      type: 'modify_block',
      description: `Modifying block ${targetBlockUid}`,
      executed: false
    });
    
    const updateData: any = {};
    if (modifications.title) {
      updateData['x-component-props'] = { 
        ...updateData['x-component-props'], 
        title: modifications.title 
      };
    }
    if (modifications.collection) {
      updateData['x-decorator-props'] = { 
        ...updateData['x-decorator-props'], 
        collection: modifications.collection 
      };
    }
    if (modifications.componentProps) {
      updateData['x-component-props'] = { 
        ...updateData['x-component-props'], 
        ...modifications.componentProps 
      };
    }
    if (modifications.decoratorProps) {
      updateData['x-decorator-props'] = { 
        ...updateData['x-decorator-props'], 
        ...modifications.decoratorProps 
      };
    }
    
    const modifyResult = await client.updateBlockSchema(targetBlockUid, updateData);
    if (result.actions[1]) {
      result.actions[1].executed = true;
      result.actions[1].result = modifyResult;
    }
    
    result.success = true;
    result.message = `✅ Successfully modified block ${targetBlockUid}`;
    
  } catch (error) {
    result.success = false;
    result.message = `❌ Smart block modification failed: ${error instanceof Error ? error.message : String(error)}`;
  }
  
  return result;
}

/**
 * 智能页面清理 - 分析页面结构，清理空白或无用元素
 */
export async function smartPageCleanup(
  client: NocoBaseClient,
  pageSchemaUid: string,
  options: {
    removeEmptyRows?: boolean;
    removeEmptyCols?: boolean;
    consolidateRows?: boolean;
  } = {}
): Promise<SmartOperationResult> {
  
  const result: SmartOperationResult = {
    success: false,
    message: '',
    context: {
      pageSchemaUid,
      analysis: {} as PageStructureAnalysis,
      operationType: 'modify_block'
    },
    actions: []
  };
  
  try {
    // 分析页面结构
    const analysis = await analyzePageStructure(client, pageSchemaUid);
    result.context.analysis = analysis;
    
    const cleanupActions = [];
    
    // 识别需要清理的元素
    for (const row of analysis.rows) {
      const emptyCols = row.cols.filter(col => col.blocks.length === 0);
      
      if (options.removeEmptyCols && emptyCols.length > 0) {
        cleanupActions.push(...emptyCols.map(col => ({
          type: 'remove_empty_col',
          uid: col.uid,
          description: `Remove empty column ${col.uid}`
        })));
      }
      
      if (options.removeEmptyRows && row.cols.every(col => col.blocks.length === 0)) {
        cleanupActions.push({
          type: 'remove_empty_row',
          uid: row.uid,
          description: `Remove empty row ${row.uid}`
        });
      }
    }
    
    // 执行清理操作
    for (const action of cleanupActions) {
      result.actions.push({
        type: action.type,
        description: action.description,
        executed: false
      });
      
      try {
        await client.deleteBlockSchema(action.uid);
        const lastAction = result.actions[result.actions.length - 1];
        if (lastAction) {
          lastAction.executed = true;
        }
      } catch (error) {
        const lastAction = result.actions[result.actions.length - 1];
        if (lastAction) {
          lastAction.result = { error: error instanceof Error ? error.message : String(error) };
        }
      }
    }
    
    result.success = true;
    result.message = `✅ Page cleanup completed. Executed ${cleanupActions.length} cleanup actions.`;
    
  } catch (error) {
    result.success = false;
    result.message = `❌ Smart page cleanup failed: ${error instanceof Error ? error.message : String(error)}`;
  }
  
  return result;
}

/**
 * 准备区块 Schema 的辅助函数
 */
async function prepareBlockSchema(blockConfig: any): Promise<any> {
  // 这里应该调用相应的区块模板创建函数
  // 为了简化，返回一个基本的 schema 结构
  return {
    type: 'void',
    'x-component': 'CardItem',
    'x-component-props': {
      title: blockConfig.title || `${blockConfig.type} Block`
    },
    properties: {}
  };
}
