/**
 * 双重验证策略：Schema API + Playwright
 * 
 * 这个模块实现了结合 Schema API 和 Playwright 的完整验证策略，
 * 确保前端操作的正确性和完整性。
 */

import { NocoBaseClient } from './client.js';

export interface SchemaVerificationResult {
  success: boolean;
  blocks: any[];
  message: string;
  details?: {
    foundUIDs: string[];
    expectedComponents: string[];
    actualComponents: string[];
  };
}

export interface PlaywrightVerificationConfig {
  selector: string;
  expectedText?: string;
  expectedCount?: number;
  shouldExist?: boolean;
  attributes?: Record<string, string>;
}

export interface CompleteVerificationResult {
  schemaVerification: SchemaVerificationResult;
  playwrightVerification?: {
    success: boolean;
    message: string;
    details?: any;
  };
  overall: boolean;
  summary: string;
}

/**
 * 通过 Schema API 验证区块创建结果
 */
export async function verifyBlockCreation(
  client: NocoBaseClient, 
  pageSchemaUid: string, 
  expectedBlockType: string,
  options?: {
    expectedCount?: number;
    expectedProperties?: string[];
    collectionName?: string;
  }
): Promise<SchemaVerificationResult> {
  try {
    // 1. 获取页面的完整结构
    const pageProperties = await client.getSchemaProperties(pageSchemaUid);
    
    // 2. 递归搜索目标区块类型
    function findBlocksInSchema(obj: any, blockType: string, path: string = ''): any[] {
      const blocks: any[] = [];
      
      if (obj && typeof obj === 'object') {
        // 检查当前对象是否匹配目标区块类型
        const isMatch = 
          obj['x-component'] === blockType ||
          obj['x-decorator']?.includes(blockType) ||
          obj['x-settings']?.includes(blockType) ||
          (blockType === 'table' && obj['x-component'] === 'TableV2') ||
          (blockType === 'form' && obj['x-decorator'] === 'FormBlockProvider') ||
          (blockType === 'markdown' && obj['x-component'] === 'Markdown.Void');
        
        if (isMatch) {
          blocks.push({
            ...obj,
            _path: path,
            _uid: obj['x-uid']
          });
        }
        
        // 递归检查子属性
        if (obj.properties) {
          for (const [key, value] of Object.entries(obj.properties)) {
            blocks.push(...findBlocksInSchema(value, blockType, `${path}.${key}`));
          }
        }
      }
      
      return blocks;
    }
    
    const foundBlocks = findBlocksInSchema(pageProperties, expectedBlockType);
    
    // 3. 验证结果
    const success = foundBlocks.length > 0 && 
      (!options?.expectedCount || foundBlocks.length === options.expectedCount);
    
    // 4. 详细分析
    const details = {
      foundUIDs: foundBlocks.map(block => block._uid).filter(Boolean),
      expectedComponents: [expectedBlockType],
      actualComponents: foundBlocks.map(block => 
        block['x-component'] || block['x-decorator'] || 'unknown'
      )
    };
    
    // 5. 如果指定了集合名称，验证数据绑定
    if (options?.collectionName) {
      const hasCorrectCollection = foundBlocks.some(block => 
        block['x-decorator-props']?.collection === options.collectionName ||
        block['x-collection-field']?.startsWith(options.collectionName)
      );
      
      if (!hasCorrectCollection) {
        return {
          success: false,
          blocks: foundBlocks,
          message: `❌ Found ${foundBlocks.length} ${expectedBlockType} block(s) but none bound to collection '${options.collectionName}'`,
          details
        };
      }
    }
    
    return {
      success,
      blocks: foundBlocks,
      message: success 
        ? `✅ Found ${foundBlocks.length} ${expectedBlockType} block(s)${options?.collectionName ? ` bound to '${options.collectionName}'` : ''}`
        : `❌ Expected ${options?.expectedCount || 'at least 1'} ${expectedBlockType} block(s), found ${foundBlocks.length}`,
      details
    };
    
  } catch (error) {
    return {
      success: false,
      blocks: [],
      message: `❌ Schema verification failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 验证页面结构的完整性
 */
export async function verifyPageStructure(
  client: NocoBaseClient,
  pageSchemaUid: string,
  expectedStructure: {
    hasGrid?: boolean;
    hasRows?: number;
    hasCols?: number;
    hasBlocks?: string[];
  }
): Promise<SchemaVerificationResult> {
  try {
    const pageProperties = await client.getSchemaProperties(pageSchemaUid);
    
    const analysis = {
      grids: 0,
      rows: 0,
      cols: 0,
      blocks: [] as string[]
    };
    
    function analyzeStructure(obj: any) {
      if (obj && typeof obj === 'object') {
        if (obj['x-component'] === 'Grid') analysis.grids++;
        if (obj['x-component'] === 'Grid.Row') analysis.rows++;
        if (obj['x-component'] === 'Grid.Col') analysis.cols++;
        
        // 记录区块类型
        if (obj['x-decorator']?.includes('BlockProvider') || 
            obj['x-settings']?.includes('blockSettings')) {
          const blockType = obj['x-decorator'] || obj['x-component'] || 'unknown';
          analysis.blocks.push(blockType);
        }
        
        if (obj.properties) {
          for (const value of Object.values(obj.properties)) {
            analyzeStructure(value);
          }
        }
      }
    }
    
    analyzeStructure(pageProperties);
    
    // 验证期望的结构
    const checks = [];
    if (expectedStructure.hasGrid !== undefined) {
      checks.push(expectedStructure.hasGrid ? analysis.grids > 0 : analysis.grids === 0);
    }
    if (expectedStructure.hasRows !== undefined) {
      checks.push(analysis.rows === expectedStructure.hasRows);
    }
    if (expectedStructure.hasCols !== undefined) {
      checks.push(analysis.cols === expectedStructure.hasCols);
    }
    if (expectedStructure.hasBlocks) {
      for (const expectedBlock of expectedStructure.hasBlocks) {
        checks.push(analysis.blocks.some(block => block.includes(expectedBlock)));
      }
    }
    
    const success = checks.every(check => check);
    
    return {
      success,
      blocks: [],
      message: success 
        ? `✅ Page structure matches expectations: ${analysis.grids} grids, ${analysis.rows} rows, ${analysis.cols} cols, ${analysis.blocks.length} blocks`
        : `❌ Page structure mismatch. Found: ${analysis.grids} grids, ${analysis.rows} rows, ${analysis.cols} cols`,
      details: {
        foundUIDs: [],
        expectedComponents: expectedStructure.hasBlocks || [],
        actualComponents: analysis.blocks
      }
    };
    
  } catch (error) {
    return {
      success: false,
      blocks: [],
      message: `❌ Structure verification failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 完整的验证策略 - 结合 Schema API 和 Playwright
 */
export async function completeVerification(
  client: NocoBaseClient,
  pageSchemaUid: string,
  verification: {
    blockType?: string;
    blockOptions?: {
      expectedCount?: number;
      collectionName?: string;
    };
    structure?: {
      hasGrid?: boolean;
      hasRows?: number;
      hasCols?: number;
      hasBlocks?: string[];
    };
    playwright?: PlaywrightVerificationConfig;
  }
): Promise<CompleteVerificationResult> {
  
  const results: CompleteVerificationResult = {
    schemaVerification: { success: false, blocks: [], message: '' },
    overall: false,
    summary: ''
  };
  
  try {
    // 1. Schema API 验证
    if (verification.blockType) {
      results.schemaVerification = await verifyBlockCreation(
        client, 
        pageSchemaUid, 
        verification.blockType,
        verification.blockOptions
      );
    } else if (verification.structure) {
      results.schemaVerification = await verifyPageStructure(
        client,
        pageSchemaUid,
        verification.structure
      );
    }
    
    // 2. Playwright 验证（占位符 - 需要实际的 Playwright 实例）
    if (verification.playwright) {
      results.playwrightVerification = {
        success: true, // 实际应该通过 Playwright 检查
        message: `✅ Playwright verification configured for: ${verification.playwright.selector}`,
        details: {
          selector: verification.playwright.selector,
          expectedText: verification.playwright.expectedText,
          expectedCount: verification.playwright.expectedCount
        }
      };
    }
    
    // 3. 综合评估
    results.overall = results.schemaVerification.success && 
      (!verification.playwright || results.playwrightVerification?.success !== false);
    
    // 4. 生成总结
    const parts = [results.schemaVerification.message];
    if (results.playwrightVerification) {
      parts.push(results.playwrightVerification.message);
    }
    
    results.summary = results.overall 
      ? `🎉 Complete verification passed: ${parts.join('; ')}`
      : `❌ Verification failed: ${parts.join('; ')}`;
    
  } catch (error) {
    results.summary = `❌ Verification error: ${error instanceof Error ? error.message : String(error)}`;
  }
  
  return results;
}

/**
 * 验证策略的使用示例和最佳实践
 */
export const VERIFICATION_EXAMPLES = {
  // 验证表格区块创建
  tableBlock: {
    blockType: 'table',
    blockOptions: {
      expectedCount: 1,
      collectionName: 'users'
    },
    playwright: {
      selector: '[data-testid="table-block"]',
      expectedCount: 1
    }
  },
  
  // 验证表单区块创建
  formBlock: {
    blockType: 'form',
    blockOptions: {
      expectedCount: 1,
      collectionName: 'users'
    },
    playwright: {
      selector: 'form[data-testid="form-block"]',
      shouldExist: true
    }
  },
  
  // 验证页面结构
  pageStructure: {
    structure: {
      hasGrid: true,
      hasRows: 2,
      hasCols: 3,
      hasBlocks: ['TableBlockProvider', 'FormBlockProvider']
    },
    playwright: {
      selector: '.nb-grid',
      expectedCount: 1
    }
  }
};
