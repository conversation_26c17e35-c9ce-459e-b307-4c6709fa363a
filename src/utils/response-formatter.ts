import * as yaml from 'js-yaml';

/**
 * 数据分析结果接口
 */
interface DataAnalysis {
  originalSizeKb: number;
  totalNodes: number;
  commonProperties: Array<{
    property: string;
    value: any;
    occurrences: number;
    notes: string;
  }>;
  structuralPatterns: Array<{
    pattern: string;
    description: string;
    format?: string;
    samples?: string[];
  }>;
}

/**
 * 格式化配置接口
 */
interface FormatterConfig {
  enableMetaAnalysis: boolean;
  maxSamples: number;
  minOccurrences: number;
  compressThreshold: number; // KB
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: FormatterConfig = {
  enableMetaAnalysis: true,
  maxSamples: 3,
  minOccurrences: 3,
  compressThreshold: 5 // 超过5KB才启用压缩
};

/**
 * 响应格式化器类
 */
export class ResponseFormatter {
  private config: FormatterConfig;

  constructor(config: Partial<FormatterConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 格式化响应数据为压缩的YAML格式
   */
  formatResponse(data: any, context?: string): string {
    const jsonString = JSON.stringify(data);
    const sizeKb = Buffer.byteLength(jsonString, 'utf8') / 1024;

    // 如果数据量小于阈值，直接返回JSON格式
    if (sizeKb < this.config.compressThreshold) {
      return `Data size: ${sizeKb.toFixed(1)}KB (below compression threshold)\n\n${JSON.stringify(data, null, 2)}`;
    }

    const analysis = this.analyzeData(data, sizeKb);
    const compressedData = this.compressData(data, analysis, context);

    const result = {
      _meta: this.config.enableMetaAnalysis ? {
        source_info: {
          original_size_kb: parseFloat(sizeKb.toFixed(1)),
          total_nodes: analysis.totalNodes,
          context: context || 'unknown'
        },
        common_properties: analysis.commonProperties,
        structural_patterns: analysis.structuralPatterns
      } : undefined,
      ...compressedData
    };

    // 过滤掉undefined的_meta
    if (!this.config.enableMetaAnalysis) {
      delete result._meta;
    }

    return yaml.dump(result, {
      indent: 2,
      lineWidth: 120,
      noRefs: true,
      sortKeys: false
    });
  }

  /**
   * 分析数据结构和模式
   */
  private analyzeData(data: any, sizeKb: number): DataAnalysis {
    const propertyCount = new Map<string, { value: any; count: number }>();
    const patterns: string[] = [];
    let totalNodes = 0;

    const traverse = (obj: any, path: string = '') => {
      if (obj === null || typeof obj !== 'object') return;
      
      totalNodes++;

      if (Array.isArray(obj)) {
        obj.forEach((item, index) => traverse(item, `${path}[${index}]`));
      } else {
        Object.entries(obj).forEach(([key, value]) => {
          const propKey = `${key}`;
          const existing = propertyCount.get(propKey);
          
          if (existing && JSON.stringify(existing.value) === JSON.stringify(value)) {
            existing.count++;
          } else if (!existing) {
            propertyCount.set(propKey, { value, count: 1 });
          }

          // 检测随机字符串模式
          if (typeof key === 'string' && /^[a-z0-9]{10,15}$/.test(key)) {
            patterns.push(key);
          }

          traverse(value, `${path}.${key}`);
        });
      }
    };

    traverse(data);

    // 提取常见属性
    const commonProperties = Array.from(propertyCount.entries())
      .filter(([_, info]) => info.count >= this.config.minOccurrences)
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 10)
      .map(([property, info]) => ({
        property,
        value: info.value,
        occurrences: info.count,
        notes: this.generatePropertyNotes(property, info.value, info.count)
      }));

    // 分析结构模式
    const structuralPatterns = [];
    
    if (patterns.length > 0) {
      structuralPatterns.push({
        pattern: 'Random Node Keys',
        description: '使用随机生成的字符串作为对象键',
        format: '10-15位小写字母和数字的组合',
        samples: patterns.slice(0, this.config.maxSamples)
      });
    }

    return {
      originalSizeKb: sizeKb,
      totalNodes,
      commonProperties,
      structuralPatterns
    };
  }

  /**
   * 生成属性说明
   */
  private generatePropertyNotes(property: string, value: any, count: number): string {
    const notes = [];
    
    if (count > 50) {
      notes.push('高频出现的基础属性');
    } else if (count > 10) {
      notes.push('常见属性');
    }

    if (property.startsWith('x-')) {
      notes.push('扩展属性');
    }

    if (property.includes('uid') || property.includes('id')) {
      notes.push('标识符相关');
    }

    if (typeof value === 'boolean') {
      notes.push(`布尔值: ${value}`);
    } else if (typeof value === 'string' && value.match(/^\d+\.\d+\.\d+$/)) {
      notes.push('版本号格式');
    }

    return notes.length > 0 ? notes.join(', ') : '通用属性';
  }

  /**
   * 压缩数据，提取核心语义结构
   */
  private compressData(data: any, analysis: DataAnalysis, context?: string): any {
    if (context === 'schema' || this.isSchemaData(data)) {
      return this.compressSchemaData(data);
    } else if (context === 'list' || this.isListData(data)) {
      return this.compressListData(data);
    } else if (context === 'collection' || this.isCollectionData(data)) {
      return this.compressCollectionData(data);
    } else if (context === 'menu' || this.isMenuData(data)) {
      return this.compressMenuData(data);
    } else {
      return this.compressGenericData(data);
    }
  }

  /**
   * 判断是否为Schema数据
   */
  private isSchemaData(data: any): boolean {
    return data && (
      data.type === 'void' ||
      data['x-component'] ||
      data.properties ||
      data._isJSONSchemaObject
    );
  }

  /**
   * 判断是否为列表数据
   */
  private isListData(data: any): boolean {
    return data && (
      (data.data && Array.isArray(data.data)) ||
      (data.meta && typeof data.meta.count === 'number')
    );
  }

  /**
   * 判断是否为集合数据
   */
  private isCollectionData(data: any): boolean {
    return data && (
      data.name && data.fields ||
      Array.isArray(data) && data.some((item: any) => item.name && item.fields)
    );
  }

  /**
   * 判断是否为菜单数据
   */
  private isMenuData(data: any): boolean {
    return data && (
      data.type && ['group', 'page', 'link', 'url'].includes(data.type) ||
      data.title && data.id && data.details
    );
  }

  /**
   * 压缩Schema数据
   */
  private compressSchemaData(data: any): any {
    const extractComponent = (node: any): any => {
      if (!node || typeof node !== 'object') return node;

      const result: any = {};

      // 保留核心组件信息
      if (node.type) result.type = node.type;
      if (node['x-component']) result.component = node['x-component'];
      if (node['x-decorator']) result.decorator = node['x-decorator'];
      if (node.title) result.title = node.title;
      if (node.name) result.name = node.name;

      // 保留配置信息
      if (node['x-component-props']) {
        result.config = this.filterProps(node['x-component-props']);
      }
      if (node['x-decorator-props']) {
        result.decoratorConfig = this.filterProps(node['x-decorator-props']);
      }

      // 递归处理子节点
      if (node.properties) {
        result.children = {};
        Object.entries(node.properties).forEach(([key, child]) => {
          const compressed = extractComponent(child);
          if (Object.keys(compressed).length > 0) {
            result.children[key] = compressed;
          }
        });
      }

      return result;
    };

    return {
      layout: extractComponent(data)
    };
  }

  /**
   * 过滤属性，保留重要信息
   */
  private filterProps(props: any): any {
    if (!props || typeof props !== 'object') return props;

    const important = ['collection', 'dataSource', 'pageSize', 'showIndex', 'dragSort', 'content', 'engine'];
    const result: any = {};

    important.forEach(key => {
      if (props[key] !== undefined) {
        result[key] = props[key];
      }
    });

    return Object.keys(result).length > 0 ? result : props;
  }

  /**
   * 压缩列表数据
   */
  private compressListData(data: any): any {
    return {
      list_summary: {
        total_items: data.meta?.count || data.data?.length || 0,
        page_info: data.meta ? {
          current_page: data.meta.page,
          page_size: data.meta.pageSize,
          total_pages: data.meta.totalPage
        } : undefined,
        sample_items: data.data ? data.data.slice(0, 3) : []
      }
    };
  }

  /**
   * 压缩集合数据
   */
  private compressCollectionData(data: any): any {
    const collections = Array.isArray(data) ? data : [data];
    
    return {
      collections_summary: {
        total_collections: collections.length,
        collections: collections.map((col: any) => ({
          name: col.name,
          title: col.title,
          fields_count: col.fields?.length || 0,
          key_fields: col.fields?.slice(0, 5)?.map((f: any) => ({
            name: f.name,
            type: f.type,
            interface: f.interface
          })) || []
        }))
      }
    };
  }

  /**
   * 压缩菜单数据
   */
  private compressMenuData(data: any): any {
    return {
      menu_item: {
        id: data.id,
        title: data.title,
        type: data.type,
        icon: data.icon || null,
        parent_id: data.parentId || null,
        schema_uid: data.schemaUid || null,
        configuration: {
          enable_tabs: data.enableTabs || false,
          enable_header: data.enableHeader !== false,
          insert_position: data.insertPosition || 'beforeEnd'
        },
        api_response: {
          status: data.details ? 'success' : 'partial',
          created_at: data.details?.createdAt,
          updated_at: data.details?.updatedAt,
          sort_order: data.details?.sort
        }
      }
    };
  }

  /**
   * 压缩通用数据
   */
  private compressGenericData(data: any): any {
    return {
      data_summary: {
        type: Array.isArray(data) ? 'array' : typeof data,
        keys: typeof data === 'object' && !Array.isArray(data) ? Object.keys(data).slice(0, 10) : undefined,
        sample: Array.isArray(data) ? data.slice(0, 2) : data
      }
    };
  }
}

/**
 * 创建格式化的响应内容
 */
export function createFormattedResponse(message: string, data?: any, context?: string) {
  const formatter = new ResponseFormatter();

  if (!data) {
    return {
      content: [
        {
          type: 'text' as const,
          text: message
        }
      ]
    };
  }

  const formattedData = formatter.formatResponse(data, context);

  return {
    content: [
      {
        type: 'text' as const,
        text: `${message}\n\n${formattedData}`
      }
    ]
  };
}

/**
 * 创建格式化的错误响应
 */
export function createFormattedErrorResponse(error: any) {
  return {
    content: [
      {
        type: 'text' as const,
        text: `Error: ${error instanceof Error ? error.message : String(error)}`
      }
    ],
    isError: true
  };
}
