#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { NocoBaseClient } from "./client.js";
import { registerCollectionTools } from "./tools/collections.js";
import { registerCollectionCategoryTools } from "./tools/collection-categories.js";
import { registerRecordTools } from "./tools/records.js";
import { registerSchemaTools } from "./tools/schema.js";
import { registerRouteTools } from "./tools/routes.js";
import { registerBlockTools } from "./tools/blocks.js";
import { registerUserTools } from "./tools/users.js";
import { registerRoleTools } from "./tools/roles.js";
import { registerTableOperationTools } from "./tools/table-operations.js";
import { registerRowOperationTools } from "./tools/row-operations.js";
import { registerFormOperationTools } from "./tools/form-operations.js";
import { registerListOperationTools } from "./tools/list-operations.js";
import { registerKanbanOperationTools } from "./tools/kanban-operations.js";
import { registerGridCardOperationTools } from "./tools/grid-card-operations.js";
import { registerCalendarOperationTools } from "./tools/calendar-operations.js";
import { registerMarkdownOperationTools } from "./tools/markdown-operations.js";
import { registerMenuOperationTools } from "./tools/menu-operations.js";
import { registerFilterOperationTools } from "./tools/filter-operations.js";
import { registerResources } from "./resources/index.js";

interface ServerConfig {
  baseUrl: string;
  token: string;
  app: string;
}

function parseArgs(): ServerConfig {
  const args = process.argv.slice(2);
  const config: Partial<ServerConfig> = {};

  for (let i = 0; i < args.length; i += 2) {
    const key = args[i];
    const value = args[i + 1];

    switch (key) {
      case "--base-url":
        if (value) config.baseUrl = value;
        break;
      case "--token":
        if (value) config.token = value;
        break;
      case "--app":
        if (value) config.app = value;
        break;
      case "--help":
        console.error(`
Usage: mcp-server-nocobase [options]

Options:
  --base-url <url>    NocoBase instance URL (required)
  --token <token>     Authentication token (required)
  --app <name>        Application name (required)
  --help              Show this help message

Environment variables:
  NOCOBASE_BASE_URL   NocoBase instance URL
  NOCOBASE_TOKEN      Authentication token
  NOCOBASE_APP        Application name
        `);
        process.exit(0);
    }
  }

  // Fallback to environment variables
  if (!config.baseUrl && process.env.NOCOBASE_BASE_URL) {
    config.baseUrl = process.env.NOCOBASE_BASE_URL;
  }
  if (!config.token && process.env.NOCOBASE_TOKEN) {
    config.token = process.env.NOCOBASE_TOKEN;
  }
  if (!config.app && process.env.NOCOBASE_APP) {
    config.app = process.env.NOCOBASE_APP;
  }

  if (!config.baseUrl || !config.token || !config.app) {
    console.error("Error: Missing required configuration. Use --help for usage information.");
    process.exit(1);
  }

  return config as ServerConfig;
}

async function main() {
  const config = parseArgs();

  // Create NocoBase client
  const client = new NocoBaseClient({
    baseUrl: config.baseUrl,
    token: config.token,
    app: config.app,
  });

  // Create MCP server
  const server = new McpServer({
    name: "mcp-server-nocobase",
    version: "0.1.0",
  });

  // Register tools and resources
  await registerCollectionTools(server, client);
  await registerCollectionCategoryTools(server, client);
  await registerRecordTools(server, client);
  await registerSchemaTools(server, client);
  await registerRouteTools(server, client);
  await registerBlockTools(server, client);
  await registerTableOperationTools(server, client);
  await registerRowOperationTools(server, client);
  await registerFormOperationTools(server, client);
  await registerListOperationTools(server, client);
  await registerKanbanOperationTools(server, client);
  await registerGridCardOperationTools(server, client);
  await registerCalendarOperationTools(server, client);
  await registerMarkdownOperationTools(server, client);
  await registerMenuOperationTools(server, client);
  await registerFilterOperationTools(server, client);
  await registerUserTools(server, client);
  await registerRoleTools(server, client);
  await registerResources(server, client);

  // Start the server
  const transport = new StdioServerTransport();
  await server.connect(transport);

  console.error("MCP Server for NocoBase is running...");
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.error("Shutting down MCP Server for NocoBase...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.error("Shutting down MCP Server for NocoBase...");
  process.exit(0);
});

main().catch((error) => {
  console.error("Fatal error:", error);
  process.exit(1);
});
