import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { NocoBaseClient } from "../client.js";

export async function registerResources(server: McpServer, client: NocoBaseClient) {
  // Collections list resource
  server.registerResource(
    "collections-list",
    "collections://list",
    {
      title: "Collections List",
      description: "List of all collections in the NocoBase application",
      mimeType: "application/json"
    },
    async (uri) => {
      try {
        const collections = await client.listCollectionsMeta();
        
        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(collections, null, 2),
            mimeType: "application/json"
          }]
        };
      } catch (error) {
        return {
          contents: [{
            uri: uri.href,
            text: `Error loading collections: ${error instanceof Error ? error.message : String(error)}`,
            mimeType: "text/plain"
          }]
        };
      }
    }
  );

  // Specific collection resource
  server.registerResource(
    "collection-details",
    new ResourceTemplate("collections://{name}", { list: undefined }),
    {
      title: "Collection Details",
      description: "Detailed information about a specific collection",
      mimeType: "application/json"
    },
    async (uri, { name }) => {
      try {
        if (!name || Array.isArray(name)) {
          throw new Error("Invalid collection name");
        }
        const collection = await client.getCollection(name);
        
        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(collection, null, 2),
            mimeType: "application/json"
          }]
        };
      } catch (error) {
        return {
          contents: [{
            uri: uri.href,
            text: `Error loading collection '${name}': ${error instanceof Error ? error.message : String(error)}`,
            mimeType: "text/plain"
          }]
        };
      }
    }
  );

  // Collection schema resource
  server.registerResource(
    "collection-schema",
    new ResourceTemplate("collections://{name}/schema", { list: undefined }),
    {
      title: "Collection Schema",
      description: "Schema definition for a collection",
      mimeType: "application/json"
    },
    async (uri, { name }) => {
      try {
        if (!name || Array.isArray(name)) {
          throw new Error("Invalid collection name");
        }
        const collection = await client.getCollection(name);
        
        // Extract schema information
        const schema = {
          name: collection.name,
          title: collection.title,
          description: collection.description,
          configuration: {
            autoGenId: collection.autoGenId,
            createdAt: collection.createdAt,
            updatedAt: collection.updatedAt,
            createdBy: collection.createdBy,
            updatedBy: collection.updatedBy,
            hidden: collection.hidden,
            inherit: collection.inherit
          },
          fields: collection.fields?.map(field => ({
            name: field.name,
            type: field.type,
            interface: field.interface,
            description: field.description,
            uiSchema: field.uiSchema
          })) || []
        };
        
        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(schema, null, 2),
            mimeType: "application/json"
          }]
        };
      } catch (error) {
        return {
          contents: [{
            uri: uri.href,
            text: `Error loading schema for collection '${name}': ${error instanceof Error ? error.message : String(error)}`,
            mimeType: "text/plain"
          }]
        };
      }
    }
  );

  // Collection records resource
  server.registerResource(
    "collection-records",
    new ResourceTemplate("collections://{name}/records", { 
      list: undefined,
      complete: {
        name: async (value) => {
          try {
            const collections = await client.listCollections();
            return collections
              .map(c => c.name)
              .filter(name => name.toLowerCase().includes(value.toLowerCase()))
              .slice(0, 10);
          } catch {
            return [];
          }
        }
      }
    }),
    {
      title: "Collection Records",
      description: "Records from a collection (limited to first 50 records)",
      mimeType: "application/json"
    },
    async (uri, { name }) => {
      try {
        if (!name || Array.isArray(name)) {
          throw new Error("Invalid collection name");
        }
        const result = await client.listRecords(name, { pageSize: 50 });
        
        const response = {
          collection: name,
          meta: result.meta,
          records: result.data
        };
        
        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(response, null, 2),
            mimeType: "application/json"
          }]
        };
      } catch (error) {
        return {
          contents: [{
            uri: uri.href,
            text: `Error loading records from collection '${name}': ${error instanceof Error ? error.message : String(error)}`,
            mimeType: "text/plain"
          }]
        };
      }
    }
  );

  // Specific record resource
  server.registerResource(
    "collection-record",
    new ResourceTemplate("collections://{name}/records/{id}", { 
      list: undefined,
      complete: {
        name: async (value) => {
          try {
            const collections = await client.listCollections();
            return collections
              .map(c => c.name)
              .filter(name => name.toLowerCase().includes(value.toLowerCase()))
              .slice(0, 10);
          } catch {
            return [];
          }
        }
      }
    }),
    {
      title: "Collection Record",
      description: "A specific record from a collection",
      mimeType: "application/json"
    },
    async (uri, { name, id }) => {
      try {
        if (!name || Array.isArray(name) || !id || Array.isArray(id)) {
          throw new Error("Invalid collection name or record ID");
        }
        const record = await client.getRecord(name, id);
        
        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(record, null, 2),
            mimeType: "application/json"
          }]
        };
      } catch (error) {
        return {
          contents: [{
            uri: uri.href,
            text: `Error loading record ${id} from collection '${name}': ${error instanceof Error ? error.message : String(error)}`,
            mimeType: "text/plain"
          }]
        };
      }
    }
  );
}
