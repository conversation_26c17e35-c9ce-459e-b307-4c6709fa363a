import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type CollectionCategory } from "../client.js";

// Collection category naming validation and suggestions
function validateCategoryNaming(params: { name: string }) {
  const suggestions: string[] = [];
  const warnings: string[] = [];
  
  // Check if name uses English
  if (/[\u4e00-\u9fa5]/.test(params.name)) {
    warnings.push("Category name contains Chinese characters. Consider using English for better internationalization.");
  }
  
  // Check naming conventions
  if (params.name !== params.name.charAt(0).toUpperCase() + params.name.slice(1)) {
    suggestions.push("Category name should start with capital letter (e.g., 'Administrative', 'Geography')");
  }
  
  // Check for spaces vs camelCase
  if (params.name.includes('_') || params.name.includes('-')) {
    suggestions.push("Consider using spaces or camelCase for category names (e.g., 'User Management' or 'UserManagement')");
  }
  
  return { suggestions, warnings };
}

export async function registerCollectionCategoryTools(server: McpServer, client: NocoBaseClient) {
  // List collection categories
  server.registerTool(
    "list_collection_categories",
    {
      title: "List Collection Categories",
      description: "List all collection categories in the NocoBase application",
      inputSchema: {
        includeCollections: z.boolean().optional().describe("Include associated collections in the response"),
        pageSize: z.number().optional().describe("Number of categories per page"),
        page: z.number().optional().describe("Page number (1-based)")
      }
    },
    async ({ includeCollections = false, pageSize, page }) => {
      try {
        const options: any = {};
        if (pageSize) options.pageSize = pageSize;
        if (page) options.page = page;
        if (includeCollections) options.appends = ['collections'];
        
        const result = await client.listCollectionCategories(options);
        const categories = result.data;

        return {
          content: [{
            type: "text",
            text: `Found ${categories.length} collection categories:\n\n${categories.map(cat => 
              `• ${cat.name} (ID: ${cat.id})\n  Color: ${cat.color || 'default'}\n  Sort: ${cat.sort || 0}${
                includeCollections && cat.collections ? `\n  Collections: ${cat.collections.map(c => c.name).join(', ')}` : ''
              }`
            ).join('\n\n')}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing collection categories: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get collection category
  server.registerTool(
    "get_collection_category",
    {
      title: "Get Collection Category",
      description: "Get detailed information about a specific collection category",
      inputSchema: {
        id: z.union([z.number(), z.string()]).describe("ID of the collection category")
      }
    },
    async ({ id }) => {
      try {
        const category = await client.getCollectionCategory(id);

        return {
          content: [{
            type: "text",
            text: `Collection Category Details:\n\n` +
              `• ID: ${category.id}\n` +
              `• Name: ${category.name}\n` +
              `• Color: ${category.color || 'default'}\n` +
              `• Sort: ${category.sort || 0}\n` +
              `• Created: ${category.createdAt || 'N/A'}\n` +
              `• Updated: ${category.updatedAt || 'N/A'}\n` +
              `• Collections: ${category.collections?.map(c => c.name).join(', ') || 'None'}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting collection category '${id}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create collection category
  server.registerTool(
    "create_collection_category",
    {
      title: "Create Collection Category",
      description: "Create a new collection category with naming best practices",
      inputSchema: {
        name: z.string()
          .min(1, "Category name is required")
          .max(100, "Category name must be at most 100 characters")
          .describe("Category name in English (e.g., 'Administrative', 'User Management', 'Geography')"),
        color: z.string().optional()
          .describe("Category color (e.g., 'blue', 'green', 'red', 'yellow', 'purple', 'orange', 'default')"),
        sort: z.number().optional()
          .describe("Sort order for the category (lower numbers appear first)")
      }
    },
    async (params) => {
      try {
        // Validate naming conventions and provide suggestions
        const namingValidation = validateCategoryNaming(params);
        let responseMessage = '';
        
        if (namingValidation.warnings.length > 0) {
          responseMessage += '\n⚠️  Naming Warnings:\n' + namingValidation.warnings.map(w => `  • ${w}`).join('\n');
        }
        
        if (namingValidation.suggestions.length > 0) {
          responseMessage += '\n💡 Naming Suggestions:\n' + namingValidation.suggestions.map(s => `  • ${s}`).join('\n');
        }

        const categoryData: any = { name: params.name };
        if (params.color !== undefined) categoryData.color = params.color;
        if (params.sort !== undefined) categoryData.sort = params.sort;

        const category = await client.createCollectionCategory(categoryData);

        return {
          content: [{
            type: "text",
            text: `✅ Successfully created collection category '${category.name}' (ID: ${category.id})${responseMessage}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating collection category '${params.name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update collection category
  server.registerTool(
    "update_collection_category",
    {
      title: "Update Collection Category",
      description: "Update an existing collection category",
      inputSchema: {
        id: z.union([z.number(), z.string()]).describe("ID of the collection category to update"),
        name: z.string()
          .min(1, "Category name cannot be empty")
          .max(100, "Category name must be at most 100 characters")
          .optional()
          .describe("New category name"),
        color: z.string().optional()
          .describe("New category color"),
        sort: z.number().optional()
          .describe("New sort order")
      }
    },
    async ({ id, ...updates }) => {
      try {
        // Validate naming if name is being updated
        let responseMessage = '';
        if (updates.name) {
          const namingValidation = validateCategoryNaming({ name: updates.name });
          
          if (namingValidation.warnings.length > 0) {
            responseMessage += '\n⚠️  Naming Warnings:\n' + namingValidation.warnings.map(w => `  • ${w}`).join('\n');
          }
          
          if (namingValidation.suggestions.length > 0) {
            responseMessage += '\n💡 Naming Suggestions:\n' + namingValidation.suggestions.map(s => `  • ${s}`).join('\n');
          }
        }

        const updateData: any = {};
        if (updates.name !== undefined) updateData.name = updates.name;
        if (updates.color !== undefined) updateData.color = updates.color;
        if (updates.sort !== undefined) updateData.sort = updates.sort;

        const category = await client.updateCollectionCategory(id, updateData);

        return {
          content: [{
            type: "text",
            text: `✅ Successfully updated collection category '${category.name}' (ID: ${category.id})${responseMessage}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating collection category '${id}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete collection category
  server.registerTool(
    "delete_collection_category",
    {
      title: "Delete Collection Category",
      description: "Delete a collection category (collections will be uncategorized)",
      inputSchema: {
        id: z.union([z.number(), z.string()]).describe("ID of the collection category to delete")
      }
    },
    async ({ id }) => {
      try {
        await client.deleteCollectionCategory(id);

        return {
          content: [{
            type: "text",
            text: `✅ Successfully deleted collection category '${id}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting collection category '${id}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Move collection category
  server.registerTool(
    "move_collection_category",
    {
      title: "Move Collection Category",
      description: "Move a collection category to change its sort order",
      inputSchema: {
        sourceId: z.union([z.number(), z.string()]).describe("ID of the category to move"),
        targetId: z.union([z.number(), z.string()]).optional().describe("ID of the target category to move relative to"),
        method: z.enum(["insertAfter", "insertBefore", "prepend", "append"]).optional().describe("Move method")
      }
    },
    async ({ sourceId, targetId, method }) => {
      try {
        const moveOptions: any = { sourceId };
        if (targetId !== undefined) moveOptions.targetId = targetId;
        if (method !== undefined) moveOptions.method = method;

        await client.moveCollectionCategory(moveOptions);

        return {
          content: [{
            type: "text",
            text: `✅ Successfully moved collection category '${sourceId}'${targetId ? ` relative to '${targetId}'` : ''}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error moving collection category '${sourceId}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
