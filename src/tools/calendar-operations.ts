/**
 * NocoBase Calendar Operations Tools
 * 提供日历区块的事件操作功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';

/**
 * 获取日历数据工具
 */
export const getCalendarDataTool: Tool = {
  name: 'get_calendar_data',
  description: 'Get calendar events data with filtering and date range support',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing calendar events'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for events'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific fields to retrieve'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Related fields to append'
      },
      dateRange: {
        type: 'object',
        properties: {
          start: { type: 'string', description: 'Start date (ISO format)' },
          end: { type: 'string', description: 'End date (ISO format)' }
        },
        description: 'Date range to filter events'
      },
      fieldNames: {
        type: 'object',
        properties: {
          id: { type: 'string', default: 'id' },
          title: { type: 'string', description: 'Field name for event title' },
          start: { type: 'string', description: 'Field name for event start time' },
          end: { type: 'string', description: 'Field name for event end time' },
          colorFieldName: { type: 'string', description: 'Field name for event color' }
        },
        description: 'Field mappings for calendar events'
      }
    },
    required: ['collectionName']
  }
};

export async function handleGetCalendarData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, filter, fields, appends, dateRange, fieldNames } = args;

    const data = await client.getCalendarData(collectionName, {
      filter,
      fields,
      appends,
      dateRange,
      fieldNames
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar data retrieved successfully:\n${JSON.stringify({
            collection: collectionName,
            eventsCount: data.length,
            dateRange,
            events: data
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving calendar data: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建日历事件工具
 */
export const createCalendarEventTool: Tool = {
  name: 'create_calendar_event',
  description: 'Create a new calendar event',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection for calendar events'
      },
      eventData: {
        type: 'object',
        description: 'Event data including title, start/end times, description, etc.'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the creation',
        default: ['title', 'description', 'startDate', 'endDate', 'priority']
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: true
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'eventData']
  }
};

export async function handleCreateCalendarEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventData, whitelist, updateAssociationValues, triggerWorkflows } = args;

    const result = await client.createCalendarEvent(collectionName, eventData, {
      whitelist,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar event created successfully:\n${JSON.stringify({
            collection: collectionName,
            eventId: result.id,
            eventData: result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating calendar event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 更新日历事件工具
 */
export const updateCalendarEventTool: Tool = {
  name: 'update_calendar_event',
  description: 'Update an existing calendar event',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the event'
      },
      eventId: {
        type: ['string', 'number'],
        description: 'The ID of the event to update'
      },
      updateData: {
        type: 'object',
        description: 'Data to update for the event'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the update',
        default: ['title', 'description', 'startDate', 'endDate', 'priority']
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: true
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'eventId', 'updateData']
  }
};

export async function handleUpdateCalendarEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventId, updateData, whitelist, updateAssociationValues, triggerWorkflows } = args;

    const result = await client.updateCalendarEvent(collectionName, eventId, updateData, {
      whitelist,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar event updated successfully:\n${JSON.stringify({
            collection: collectionName,
            eventId,
            updateData: result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating calendar event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 删除日历事件工具
 */
export const deleteCalendarEventTool: Tool = {
  name: 'delete_calendar_event',
  description: 'Delete a calendar event',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the event'
      },
      eventId: {
        type: ['string', 'number'],
        description: 'The ID of the event to delete'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'eventId']
  }
};

export async function handleDeleteCalendarEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventId, triggerWorkflows } = args;

    await client.deleteCalendarEvent(collectionName, eventId, {
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar event deleted successfully: ${eventId}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting calendar event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 移动日历事件工具
 */
export const moveCalendarEventTool: Tool = {
  name: 'move_calendar_event',
  description: 'Move a calendar event to new date/time',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the event'
      },
      eventId: {
        type: ['string', 'number'],
        description: 'The ID of the event to move'
      },
      newTimes: {
        type: 'object',
        properties: {
          start: { type: 'string', description: 'New start time (ISO format)' },
          end: { type: 'string', description: 'New end time (ISO format)' }
        },
        required: ['start'],
        description: 'New date/time for the event'
      },
      fieldNames: {
        type: 'object',
        properties: {
          start: { type: 'string', default: 'startDate' },
          end: { type: 'string', default: 'endDate' }
        },
        description: 'Field names for start and end times'
      }
    },
    required: ['collectionName', 'eventId', 'newTimes']
  }
};

export async function handleMoveCalendarEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventId, newTimes, fieldNames } = args;

    const result = await client.moveCalendarEvent(collectionName, eventId, newTimes, fieldNames);

    return {
      content: [
        {
          type: 'text',
          text: `Calendar event moved successfully:\n${JSON.stringify({
            collection: collectionName,
            eventId,
            newTimes,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error moving calendar event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 获取日历事件详情工具
 */
export const getCalendarEventTool: Tool = {
  name: 'get_calendar_event',
  description: 'Get details of a specific calendar event',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the event'
      },
      eventId: {
        type: ['string', 'number'],
        description: 'The ID of the event to retrieve'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific fields to retrieve'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Related fields to append'
      },
      except: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to exclude'
      }
    },
    required: ['collectionName', 'eventId']
  }
};

export async function handleGetCalendarEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventId, fields, appends, except } = args;

    const event = await client.getCalendarEvent(collectionName, eventId, {
      fields,
      appends,
      except
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar event retrieved successfully:\n${JSON.stringify({
            collection: collectionName,
            eventId,
            event
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving calendar event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 处理重复事件删除工具
 */
export const deleteRecurringEventTool: Tool = {
  name: 'delete_recurring_event',
  description: 'Delete a recurring calendar event (all occurrences or specific date)',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the event'
      },
      eventId: {
        type: ['string', 'number'],
        description: 'The ID of the recurring event'
      },
      deleteOption: {
        type: 'string',
        description: 'Delete option: "all" to delete all occurrences, or specific date string to exclude that date'
      }
    },
    required: ['collectionName', 'eventId', 'deleteOption']
  }
};

export async function handleDeleteRecurringEvent(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventId, deleteOption } = args;

    const result = await client.deleteRecurringEvent(collectionName, eventId, deleteOption);

    return {
      content: [
        {
          type: 'text',
          text: `Recurring event processed successfully:\n${JSON.stringify({
            collection: collectionName,
            eventId,
            deleteOption,
            action: deleteOption === 'all' ? 'deleted_all' : 'excluded_date',
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error processing recurring event: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 批量创建日历事件工具
 */
export const batchCreateCalendarEventsTool: Tool = {
  name: 'batch_create_calendar_events',
  description: 'Create multiple calendar events in batch',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection for calendar events'
      },
      eventsData: {
        type: 'array',
        items: { type: 'object' },
        description: 'Array of event data objects'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the creation',
        default: ['title', 'description', 'startDate', 'endDate', 'priority']
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: true
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'eventsData']
  }
};

export async function handleBatchCreateCalendarEvents(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, eventsData, whitelist, updateAssociationValues, triggerWorkflows } = args;

    const results = await client.batchCreateCalendarEvents(collectionName, eventsData, {
      whitelist,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar events created successfully:\n${JSON.stringify({
            collection: collectionName,
            createdCount: results.length,
            events: results
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating calendar events: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 批量更新日历事件工具
 */
export const batchUpdateCalendarEventsTool: Tool = {
  name: 'batch_update_calendar_events',
  description: 'Update multiple calendar events in batch',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing the events'
      },
      updates: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: ['string', 'number'], description: 'Event ID' },
            values: { type: 'object', description: 'Values to update' }
          },
          required: ['id', 'values']
        },
        description: 'Array of event updates'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in the update',
        default: ['title', 'description', 'startDate', 'endDate', 'priority']
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: true
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'updates']
  }
};

export async function handleBatchUpdateCalendarEvents(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, updates, whitelist, updateAssociationValues, triggerWorkflows } = args;

    const results = await client.batchUpdateCalendarEvents(collectionName, updates, {
      whitelist,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar events updated successfully:\n${JSON.stringify({
            collection: collectionName,
            updatedCount: results.length,
            events: results
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating calendar events: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加日历操作按钮工具
 */
export const addCalendarActionTool: Tool = {
  name: 'add_calendar_action',
  description: 'Add an action button to a calendar block',
  inputSchema: {
    type: 'object',
    properties: {
      calendarUid: {
        type: 'string',
        description: 'The UID of the calendar block'
      },
      actionConfig: {
        type: 'object',
        properties: {
          name: { type: 'string', description: 'Action name' },
          title: { type: 'string', description: 'Action title' },
          component: { type: 'string', description: 'Action component' },
          action: { type: 'string', description: 'Action type' },
          align: { type: 'string', enum: ['left', 'right'], default: 'left' },
          icon: { type: 'string', description: 'Action icon' },
          requiresACL: { type: 'boolean', description: 'Whether action requires ACL check' },
          aclAction: { type: 'string', description: 'ACL action for permission check' },
          settings: { type: 'string', description: 'Settings schema for the action' },
          position: { type: 'string', enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'], default: 'beforeEnd' }
        },
        required: ['name', 'title', 'component'],
        description: 'Action configuration'
      }
    },
    required: ['calendarUid', 'actionConfig']
  }
};

export async function handleAddCalendarAction(client: NocoBaseClient, args: any) {
  try {
    const { calendarUid, actionConfig } = args;

    const result = await client.addCalendarAction(calendarUid, actionConfig);

    return {
      content: [
        {
          type: 'text',
          text: `Calendar action added successfully:\n${JSON.stringify({
            calendarUid,
            actionConfig,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding calendar action: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 配置日历字段映射工具
 */
export const configureCalendarFieldsTool: Tool = {
  name: 'configure_calendar_fields',
  description: 'Configure field mappings for a calendar block',
  inputSchema: {
    type: 'object',
    properties: {
      calendarUid: {
        type: 'string',
        description: 'The UID of the calendar block'
      },
      fieldNames: {
        type: 'object',
        properties: {
          id: { type: 'string', default: 'id', description: 'ID field name' },
          title: { type: 'string', description: 'Title field name' },
          start: { type: 'string', description: 'Start date/time field name' },
          end: { type: 'string', description: 'End date/time field name' },
          colorFieldName: { type: 'string', description: 'Color field name' }
        },
        required: ['title', 'start'],
        description: 'Field mappings for calendar events'
      }
    },
    required: ['calendarUid', 'fieldNames']
  }
};

export async function handleConfigureCalendarFields(client: NocoBaseClient, args: any) {
  try {
    const { calendarUid, fieldNames } = args;

    const result = await client.configureCalendarFields(calendarUid, fieldNames);

    return {
      content: [
        {
          type: 'text',
          text: `Calendar fields configured successfully:\n${JSON.stringify({
            calendarUid,
            fieldNames,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring calendar fields: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 更新日历设置工具
 */
export const updateCalendarSettingsTool: Tool = {
  name: 'update_calendar_settings',
  description: 'Update calendar block settings',
  inputSchema: {
    type: 'object',
    properties: {
      calendarUid: {
        type: 'string',
        description: 'The UID of the calendar block'
      },
      settings: {
        type: 'object',
        properties: {
          showLunar: { type: 'boolean', description: 'Whether to show lunar calendar' },
          defaultView: { type: 'string', enum: ['month', 'week', 'day'], description: 'Default calendar view' },
          enableQuickCreateEvent: { type: 'boolean', description: 'Whether to enable quick event creation' },
          weekStart: { type: 'number', description: 'First day of week (0=Sunday, 1=Monday)' }
        },
        description: 'Calendar settings to update'
      }
    },
    required: ['calendarUid', 'settings']
  }
};

export async function handleUpdateCalendarSettings(client: NocoBaseClient, args: any) {
  try {
    const { calendarUid, settings } = args;

    const result = await client.updateCalendarSettings(calendarUid, settings);

    return {
      content: [
        {
          type: 'text',
          text: `Calendar settings updated successfully:\n${JSON.stringify({
            calendarUid,
            settings,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating calendar settings: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 筛选日历事件工具
 */
export const filterCalendarEventsTool: Tool = {
  name: 'filter_calendar_events',
  description: 'Filter calendar events with specific conditions',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection containing calendar events'
      },
      filterConditions: {
        type: 'object',
        description: 'Filter conditions for events'
      },
      dateRange: {
        type: 'object',
        properties: {
          start: { type: 'string', description: 'Start date (ISO format)' },
          end: { type: 'string', description: 'End date (ISO format)' }
        },
        description: 'Date range to filter events'
      },
      fieldNames: {
        type: 'object',
        properties: {
          start: { type: 'string', default: 'startDate' },
          end: { type: 'string', default: 'endDate' }
        },
        description: 'Field names for date filtering'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific fields to retrieve'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Related fields to append'
      },
      sort: {
        type: 'array',
        items: { type: 'string' },
        description: 'Sort fields'
      }
    },
    required: ['collectionName']
  }
};

export async function handleFilterCalendarEvents(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, filterConditions, dateRange, fieldNames, fields, appends, sort } = args;

    const data = await client.getCalendarData(collectionName, {
      filter: filterConditions,
      fields,
      appends,
      dateRange,
      fieldNames
    });

    return {
      content: [
        {
          type: 'text',
          text: `Calendar events filtered successfully:\n${JSON.stringify({
            collection: collectionName,
            filterConditions,
            dateRange,
            eventsCount: data.length,
            events: data
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error filtering calendar events: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有日历操作工具
 */
export async function registerCalendarOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 获取日历数据工具
  server.registerTool(
    'get_calendar_data',
    {
      title: 'Get Calendar Data',
      description: 'Get calendar events data with filtering and date range support',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing calendar events'),
        filter: z.any().optional().describe('Filter conditions for events'),
        fields: z.array(z.string()).optional().describe('Specific fields to retrieve'),
        appends: z.array(z.string()).optional().describe('Related fields to append'),
        dateRange: z.object({
          start: z.string().describe('Start date (ISO format)'),
          end: z.string().describe('End date (ISO format)')
        }).optional().describe('Date range to filter events'),
        fieldNames: z.object({
          id: z.string().optional().default('id'),
          title: z.string().optional().describe('Field name for event title'),
          start: z.string().optional().describe('Field name for event start time'),
          end: z.string().optional().describe('Field name for event end time'),
          colorFieldName: z.string().optional().describe('Field name for event color')
        }).optional().describe('Field mappings for calendar events')
      }
    },
    async (args: any) => {
      return await handleGetCalendarData(client, args);
    }
  );

  // 创建日历事件工具
  server.registerTool(
    'create_calendar_event',
    {
      title: 'Create Calendar Event',
      description: 'Create a new calendar event',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection for calendar events'),
        eventData: z.any().describe('Event data including title, start/end times, description, etc.'),
        whitelist: z.array(z.string()).optional().default(['title', 'description', 'startDate', 'endDate', 'priority']).describe('Fields to include in the creation'),
        updateAssociationValues: z.boolean().optional().default(true).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger (comma-separated)')
      }
    },
    async (args: any) => {
      return await handleCreateCalendarEvent(client, args);
    }
  );

  // 更新日历事件工具
  server.registerTool(
    'update_calendar_event',
    {
      title: 'Update Calendar Event',
      description: 'Update an existing calendar event',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the event'),
        eventId: z.union([z.string(), z.number()]).describe('The ID of the event to update'),
        updateData: z.any().describe('Data to update for the event'),
        whitelist: z.array(z.string()).optional().default(['title', 'description', 'startDate', 'endDate', 'priority']).describe('Fields to include in the update'),
        updateAssociationValues: z.boolean().optional().default(true).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger (comma-separated)')
      }
    },
    async (args: any) => {
      return await handleUpdateCalendarEvent(client, args);
    }
  );

  // 删除日历事件工具
  server.registerTool(
    'delete_calendar_event',
    {
      title: 'Delete Calendar Event',
      description: 'Delete a calendar event',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the event'),
        eventId: z.union([z.string(), z.number()]).describe('The ID of the event to delete'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger (comma-separated)')
      }
    },
    async (args: any) => {
      return await handleDeleteCalendarEvent(client, args);
    }
  );

  // 移动日历事件工具
  server.registerTool(
    'move_calendar_event',
    {
      title: 'Move Calendar Event',
      description: 'Move a calendar event to new date/time',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the event'),
        eventId: z.union([z.string(), z.number()]).describe('The ID of the event to move'),
        newTimes: z.object({
          start: z.string().describe('New start time (ISO format)'),
          end: z.string().optional().describe('New end time (ISO format)')
        }).describe('New date/time for the event'),
        fieldNames: z.object({
          start: z.string().optional().default('startDate'),
          end: z.string().optional().default('endDate')
        }).optional().describe('Field names for start and end times')
      }
    },
    async (args: any) => {
      return await handleMoveCalendarEvent(client, args);
    }
  );

  // 获取日历事件详情工具
  server.registerTool(
    'get_calendar_event',
    {
      title: 'Get Calendar Event',
      description: 'Get details of a specific calendar event',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the event'),
        eventId: z.union([z.string(), z.number()]).describe('The ID of the event to retrieve'),
        fields: z.array(z.string()).optional().describe('Specific fields to retrieve'),
        appends: z.array(z.string()).optional().describe('Related fields to append'),
        except: z.array(z.string()).optional().describe('Fields to exclude')
      }
    },
    async (args: any) => {
      return await handleGetCalendarEvent(client, args);
    }
  );

  // 处理重复事件删除工具
  server.registerTool(
    'delete_recurring_event',
    {
      title: 'Delete Recurring Event',
      description: 'Delete a recurring calendar event (all occurrences or specific date)',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the event'),
        eventId: z.union([z.string(), z.number()]).describe('The ID of the recurring event'),
        deleteOption: z.string().describe('Delete option: "all" to delete all occurrences, or specific date string to exclude that date')
      }
    },
    async (args: any) => {
      return await handleDeleteRecurringEvent(client, args);
    }
  );

  // 批量创建日历事件工具
  server.registerTool(
    'batch_create_calendar_events',
    {
      title: 'Batch Create Calendar Events',
      description: 'Create multiple calendar events in batch',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection for calendar events'),
        eventsData: z.array(z.any()).describe('Array of event data objects'),
        whitelist: z.array(z.string()).optional().default(['title', 'description', 'startDate', 'endDate', 'priority']).describe('Fields to include in the creation'),
        updateAssociationValues: z.boolean().optional().default(true).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger (comma-separated)')
      }
    },
    async (args: any) => {
      return await handleBatchCreateCalendarEvents(client, args);
    }
  );

  // 批量更新日历事件工具
  server.registerTool(
    'batch_update_calendar_events',
    {
      title: 'Batch Update Calendar Events',
      description: 'Update multiple calendar events in batch',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing the events'),
        updates: z.array(z.object({
          id: z.union([z.string(), z.number()]).describe('Event ID'),
          values: z.any().describe('Values to update')
        })).describe('Array of event updates'),
        whitelist: z.array(z.string()).optional().default(['title', 'description', 'startDate', 'endDate', 'priority']).describe('Fields to include in the update'),
        updateAssociationValues: z.boolean().optional().default(true).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger (comma-separated)')
      }
    },
    async (args: any) => {
      return await handleBatchUpdateCalendarEvents(client, args);
    }
  );

  // 添加日历操作按钮工具
  server.registerTool(
    'add_calendar_action',
    {
      title: 'Add Calendar Action',
      description: 'Add an action button to a calendar block',
      inputSchema: {
        calendarUid: z.string().describe('The UID of the calendar block'),
        actionConfig: z.object({
          name: z.string().describe('Action name'),
          title: z.string().describe('Action title'),
          component: z.string().describe('Action component'),
          action: z.string().optional().describe('Action type'),
          align: z.enum(['left', 'right']).optional().default('left').describe('Action alignment'),
          icon: z.string().optional().describe('Action icon'),
          requiresACL: z.boolean().optional().describe('Whether action requires ACL check'),
          aclAction: z.string().optional().describe('ACL action for permission check'),
          settings: z.string().optional().describe('Settings schema for the action'),
          position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the action')
        }).describe('Action configuration')
      }
    },
    async (args: any) => {
      return await handleAddCalendarAction(client, args);
    }
  );

  // 配置日历字段映射工具
  server.registerTool(
    'configure_calendar_fields',
    {
      title: 'Configure Calendar Fields',
      description: 'Configure field mappings for a calendar block',
      inputSchema: {
        calendarUid: z.string().describe('The UID of the calendar block'),
        fieldNames: z.object({
          id: z.string().optional().default('id').describe('ID field name'),
          title: z.string().describe('Title field name'),
          start: z.string().describe('Start date/time field name'),
          end: z.string().optional().describe('End date/time field name'),
          colorFieldName: z.string().optional().describe('Color field name')
        }).describe('Field mappings for calendar events')
      }
    },
    async (args: any) => {
      return await handleConfigureCalendarFields(client, args);
    }
  );

  // 更新日历设置工具
  server.registerTool(
    'update_calendar_settings',
    {
      title: 'Update Calendar Settings',
      description: 'Update calendar block settings',
      inputSchema: {
        calendarUid: z.string().describe('The UID of the calendar block'),
        settings: z.object({
          showLunar: z.boolean().optional().describe('Whether to show lunar calendar'),
          defaultView: z.enum(['month', 'week', 'day']).optional().describe('Default calendar view'),
          enableQuickCreateEvent: z.boolean().optional().describe('Whether to enable quick event creation'),
          weekStart: z.number().optional().describe('First day of week (0=Sunday, 1=Monday)')
        }).describe('Calendar settings to update')
      }
    },
    async (args: any) => {
      return await handleUpdateCalendarSettings(client, args);
    }
  );

  // 筛选日历事件工具
  server.registerTool(
    'filter_calendar_events',
    {
      title: 'Filter Calendar Events',
      description: 'Filter calendar events with specific conditions',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection containing calendar events'),
        filterConditions: z.any().optional().describe('Filter conditions for events'),
        dateRange: z.object({
          start: z.string().describe('Start date (ISO format)'),
          end: z.string().describe('End date (ISO format)')
        }).optional().describe('Date range to filter events'),
        fieldNames: z.object({
          start: z.string().optional().default('startDate'),
          end: z.string().optional().default('endDate')
        }).optional().describe('Field names for date filtering'),
        fields: z.array(z.string()).optional().describe('Specific fields to retrieve'),
        appends: z.array(z.string()).optional().describe('Related fields to append'),
        sort: z.array(z.string()).optional().describe('Sort fields')
      }
    },
    async (args: any) => {
      return await handleFilterCalendarEvents(client, args);
    }
  );
}
