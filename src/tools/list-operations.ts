/**
 * NocoBase List Block Operations Tools
 * 提供列表区块的数据操作、配置和管理功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';
import { uid } from '../utils.js';
import { createFormattedResponse, createFormattedErrorResponse } from '../utils/response-formatter.js';

/**
 * 预定义的列表操作配置
 */
export const LIST_ACTION_TEMPLATES = {
  // 基础操作
  create: {
    title: "{{t('Add new')}}",
    action: 'create',
    icon: 'PlusOutlined',
    type: 'primary',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'create',
    settings: 'actionSettings:addNew',
    useComponentProps: 'useCreateActionProps'
  },
  
  filter: {
    title: "{{t('Filter')}}",
    action: 'filter',
    icon: 'FilterOutlined',
    align: 'left' as const,
    requiresACL: false,
    settings: 'actionSettings:filter',
    useComponentProps: 'useFilterBlockActionProps'
  },
  
  refresh: {
    title: "{{t('Refresh')}}",
    action: 'refresh',
    icon: 'ReloadOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:refresh',
    useComponentProps: 'useRefreshActionProps'
  },
  
  export: {
    title: "{{t('Export')}}",
    action: 'export',
    icon: 'ExportOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:export',
    useComponentProps: 'useExportActionProps'
  },
  
  import: {
    title: "{{t('Import')}}",
    action: 'importXlsx',
    icon: 'ImportOutlined',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'importXlsx',
    settings: 'actionSettings:import',
    useComponentProps: 'useImportActionProps'
  },
  
  customRequest: {
    title: "{{t('Custom Request')}}",
    action: 'customize:table:request:global',
    icon: 'ApiOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:customRequest',
    useComponentProps: 'useCustomizeRequestActionProps'
  }
};

/**
 * 预定义的列表项操作配置
 */
export const LIST_ITEM_ACTION_TEMPLATES = {
  view: {
    title: "{{t('View')}}",
    action: 'view',
    icon: 'EyeOutlined',
    component: 'Action.Link',
    requiresACL: true,
    aclAction: 'view',
    settings: 'actionSettings:view',
    useComponentProps: 'useViewActionProps'
  },
  
  edit: {
    title: "{{t('Edit')}}",
    action: 'update',
    icon: 'EditOutlined',
    component: 'Action.Link',
    requiresACL: true,
    aclAction: 'update',
    settings: 'actionSettings:edit',
    useComponentProps: 'useUpdateActionProps'
  },
  
  delete: {
    title: "{{t('Delete')}}",
    action: 'destroy',
    icon: 'DeleteOutlined',
    component: 'Action.Link',
    requiresACL: true,
    aclAction: 'destroy',
    settings: 'actionSettings:delete',
    useComponentProps: 'useDestroyActionProps',
    confirm: {
      title: '确认删除',
      content: '确定要删除这条记录吗？'
    }
  },
  
  customRequest: {
    title: "{{t('Custom Request')}}",
    action: 'customize:table:request',
    icon: 'ApiOutlined',
    component: 'Action.Link',
    requiresACL: false,
    settings: 'actionSettings:customRequest',
    useComponentProps: 'useCustomizeRequestActionProps'
  }
};

/**
 * 获取列表数据工具
 */
export const getListDataTool: Tool = {
  name: 'get_list_data',
  description: 'Get data for a list block with pagination, filtering, and sorting',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      page: {
        type: 'number',
        description: 'Page number (default: 1)',
        default: 1
      },
      pageSize: {
        type: 'number',
        description: 'Number of items per page (default: 10)',
        default: 10
      },
      filter: {
        type: 'object',
        description: 'Filter conditions'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific fields to retrieve'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Related fields to append'
      },
      except: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to exclude'
      },
      sort: {
        type: 'array',
        items: { type: 'string' },
        description: 'Sort fields (e.g., ["name", "-createdAt"])'
      },
      tree: {
        type: 'boolean',
        description: 'Whether to return tree structure',
        default: false
      }
    },
    required: ['collectionName']
  }
};

/**
 * 创建列表项工具
 */
export const createListItemTool: Tool = {
  name: 'create_list_item',
  description: 'Create a new item in a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      values: {
        type: 'object',
        description: 'The values for the new item'
      },
      filterKeys: {
        type: 'array',
        items: { type: 'string' },
        description: 'Filter keys for the operation'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: false
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      },
      overwriteValues: {
        type: 'object',
        description: 'Values to overwrite'
      },
      assignedValues: {
        type: 'object',
        description: 'Values to assign'
      }
    },
    required: ['collectionName', 'values']
  }
};

/**
 * 更新列表项工具
 */
export const updateListItemTool: Tool = {
  name: 'update_list_item',
  description: 'Update an item in a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to update'
      },
      values: {
        type: 'object',
        description: 'The values to update'
      },
      overwriteValues: {
        type: 'object',
        description: 'Values to overwrite'
      },
      assignedValues: {
        type: 'object',
        description: 'Values to assign'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values',
        default: false
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'itemId', 'values']
  }
};

/**
 * 删除列表项工具
 */
export const deleteListItemTool: Tool = {
  name: 'delete_list_item',
  description: 'Delete an item from a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to delete'
      },
      filter: {
        type: 'object',
        description: 'Additional filter conditions'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'itemId']
  }
};

/**
 * 刷新列表工具
 */
export const refreshListTool: Tool = {
  name: 'refresh_list',
  description: 'Refresh data in a list block',
  inputSchema: {
    type: 'object',
    properties: {
      listUid: {
        type: 'string',
        description: 'The UID of the list block to refresh'
      }
    },
    required: ['listUid']
  }
};

/**
 * 筛选列表工具
 */
export const filterListTool: Tool = {
  name: 'filter_list',
  description: 'Apply filters to a list block',
  inputSchema: {
    type: 'object',
    properties: {
      listUid: {
        type: 'string',
        description: 'The UID of the list block'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions to apply'
      },
      page: {
        type: 'number',
        description: 'Page number after filtering',
        default: 1
      },
      pageSize: {
        type: 'number',
        description: 'Page size after filtering',
        default: 10
      }
    },
    required: ['listUid', 'filter']
  }
};

/**
 * 添加列表操作按钮工具
 */
export const addListActionTool: Tool = {
  name: 'add_list_action',
  description: 'Add an action button to a list block',
  inputSchema: {
    type: 'object',
    properties: {
      listUid: {
        type: 'string',
        description: 'The UID of the list block'
      },
      actionType: {
        type: 'string',
        enum: ['create', 'filter', 'refresh', 'export', 'import', 'customRequest', 'custom'],
        description: 'Type of action to add'
      },
      customConfig: {
        type: 'object',
        description: 'Custom action configuration (required when actionType is "custom")',
        properties: {
          title: { type: 'string', description: 'Action title' },
          action: { type: 'string', description: 'Action identifier' },
          icon: { type: 'string', description: 'Icon name' },
          type: { type: 'string', description: 'Button type' },
          align: { type: 'string', enum: ['left', 'right'], description: 'Button alignment' },
          requiresACL: { type: 'boolean', description: 'Whether action requires ACL check' },
          aclAction: { type: 'string', description: 'ACL action name' },
          settings: { type: 'string', description: 'Settings identifier' },
          useComponentProps: { type: 'string', description: 'Component props hook' },
          componentProps: { type: 'object', description: 'Additional component properties' }
        }
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the action',
        default: 'beforeEnd'
      }
    },
    required: ['listUid', 'actionType']
  }
};

/**
 * 配置列表项操作按钮工具
 */
export const configureListItemActionsTool: Tool = {
  name: 'configure_list_item_actions',
  description: 'Configure action buttons for list items',
  inputSchema: {
    type: 'object',
    properties: {
      listUid: {
        type: 'string',
        description: 'The UID of the list block'
      },
      actions: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            actionType: {
              type: 'string',
              enum: ['view', 'edit', 'delete', 'customRequest', 'custom'],
              description: 'Type of action'
            },
            customConfig: {
              type: 'object',
              description: 'Custom action configuration (for custom actions)',
              properties: {
                title: { type: 'string' },
                action: { type: 'string' },
                icon: { type: 'string' },
                component: { type: 'string' },
                requiresACL: { type: 'boolean' },
                aclAction: { type: 'string' },
                settings: { type: 'string' },
                useComponentProps: { type: 'string' },
                componentProps: { type: 'object' }
              }
            }
          },
          required: ['actionType']
        },
        description: 'Array of actions to configure'
      }
    },
    required: ['listUid', 'actions']
  }
};

/**
 * 配置列表字段工具
 */
export const configureListFieldsTool: Tool = {
  name: 'configure_list_fields',
  description: 'Configure field display in a list block',
  inputSchema: {
    type: 'object',
    properties: {
      listUid: {
        type: 'string',
        description: 'The UID of the list block'
      },
      fields: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Field name' },
            title: { type: 'string', description: 'Field display title' },
            component: { type: 'string', description: 'Display component' },
            span: { type: 'number', description: 'Grid span (1-24)', default: 12 },
            required: { type: 'boolean', description: 'Whether field is required' },
            componentProps: { type: 'object', description: 'Component properties' }
          },
          required: ['name']
        },
        description: 'Fields configuration'
      }
    },
    required: ['listUid', 'fields']
  }
};

/**
 * 导出列表数据工具
 */
export const exportListDataTool: Tool = {
  name: 'export_list_data',
  description: 'Export data from a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for export'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to include in export'
      },
      format: {
        type: 'string',
        enum: ['csv', 'xlsx'],
        description: 'Export format',
        default: 'xlsx'
      },
      pageSize: {
        type: 'number',
        description: 'Number of records per page for export'
      },
      page: {
        type: 'number',
        description: 'Page number for export'
      }
    },
    required: ['collectionName']
  }
};

/**
 * 导入列表数据工具
 */
export const importListDataTool: Tool = {
  name: 'import_list_data',
  description: 'Import data to a list block collection',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      file: {
        type: 'string',
        description: 'File path or base64 content to import'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Field mappings for import'
      },
      updateStrategy: {
        type: 'string',
        enum: ['upsert', 'insert', 'update'],
        description: 'Update strategy for existing records',
        default: 'upsert'
      }
    },
    required: ['collectionName', 'file']
  }
};

/**
 * 自定义列表请求工具
 */
export const customListRequestTool: Tool = {
  name: 'custom_list_request',
  description: 'Send a custom request for list block operations',
  inputSchema: {
    type: 'object',
    properties: {
      url: {
        type: 'string',
        description: 'Request URL'
      },
      method: {
        type: 'string',
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        description: 'HTTP method',
        default: 'GET'
      },
      headers: {
        type: 'object',
        description: 'Request headers'
      },
      params: {
        type: 'object',
        description: 'URL parameters'
      },
      data: {
        type: 'object',
        description: 'Request body data'
      }
    },
    required: ['url']
  }
};

// Handler functions

/**
 * 处理获取列表数据
 */
export async function handleGetListData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, page, pageSize, filter, fields, appends, except, sort, tree } = args;

    const data = await client.getListData(collectionName, {
      page,
      pageSize,
      filter,
      fields,
      appends,
      except,
      sort,
      tree
    });

    return createFormattedResponse(
      `List data retrieved successfully from collection '${collectionName}':`,
      {
        collection: collectionName,
        meta: data.meta,
        data: data.data
      },
      'list'
    );
  } catch (error) {
    return createFormattedErrorResponse(error);
  }
}

/**
 * 处理创建列表项
 */
export async function handleCreateListItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, values, filterKeys, updateAssociationValues, triggerWorkflows, overwriteValues, assignedValues } = args;

    const data = await client.createListItem(collectionName, values, {
      filterKeys,
      updateAssociationValues,
      triggerWorkflows,
      overwriteValues,
      assignedValues
    });

    return {
      content: [
        {
          type: 'text',
          text: `List item created successfully:\n${JSON.stringify({
            collection: collectionName,
            id: data.id,
            data
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating list item: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理更新列表项
 */
export async function handleUpdateListItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, values, overwriteValues, assignedValues, updateAssociationValues, triggerWorkflows } = args;

    const data = await client.updateListItem(collectionName, itemId, values, {
      overwriteValues,
      assignedValues,
      updateAssociationValues,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `List item updated successfully:\n${JSON.stringify({
            collection: collectionName,
            id: itemId,
            data
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating list item: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理删除列表项
 */
export async function handleDeleteListItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, filter, triggerWorkflows } = args;

    await client.deleteListItem(collectionName, itemId, {
      filter,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `List item deleted successfully: ${itemId}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting list item: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理批量删除列表项
 */
export async function handleBulkDeleteListItems(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemIds, filter, triggerWorkflows } = args;

    await client.bulkDeleteListItems(collectionName, itemIds, {
      filter,
      triggerWorkflows
    });

    return {
      content: [
        {
          type: 'text',
          text: `List items deleted successfully: ${itemIds.length} items`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting list items: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理查看列表项
 */
export async function handleViewListItem(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, itemId, filter, fields, appends, except } = args;

    const data = await client.viewListItem(collectionName, itemId, {
      filter,
      fields,
      appends,
      except
    });

    return {
      content: [
        {
          type: 'text',
          text: `List item retrieved successfully:\n${JSON.stringify({
            collection: collectionName,
            id: itemId,
            data
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error viewing list item: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理刷新列表
 */
export async function handleRefreshList(client: NocoBaseClient, args: any) {
  try {
    const { listUid } = args;

    await client.refreshList(listUid);

    return {
      content: [
        {
          type: 'text',
          text: `List refreshed successfully: ${listUid}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error refreshing list: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理筛选列表
 */
export async function handleFilterList(client: NocoBaseClient, args: any) {
  try {
    const { listUid, filter, page, pageSize } = args;

    const result = await client.filterList(listUid, {
      filter,
      page,
      pageSize
    });

    return {
      content: [
        {
          type: 'text',
          text: `List filtered successfully:\n${JSON.stringify({
            listUid,
            filter,
            page,
            pageSize,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error filtering list: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理添加列表操作按钮
 */
export async function handleAddListAction(client: NocoBaseClient, args: any) {
  try {
    const { listUid, actionType, customConfig, position } = args;

    let actionConfig;

    if (actionType === 'custom') {
      if (!customConfig) {
        throw new Error('customConfig is required when actionType is "custom"');
      }
      actionConfig = customConfig;
    } else {
      actionConfig = LIST_ACTION_TEMPLATES[actionType as keyof typeof LIST_ACTION_TEMPLATES];
      if (!actionConfig) {
        throw new Error(`Unknown action type: ${actionType}`);
      }
    }

    const result = await client.addListAction(listUid, actionConfig, position);

    return {
      content: [
        {
          type: 'text',
          text: `List action added successfully:\n${JSON.stringify({
            listUid,
            actionType,
            title: actionConfig.title,
            action: actionConfig.action,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding list action: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理配置列表项操作按钮
 */
export async function handleConfigureListItemActions(client: NocoBaseClient, args: any) {
  try {
    const { listUid, actions } = args;

    const actionConfigs = actions.map((action: any) => {
      if (action.actionType === 'custom') {
        return action.customConfig;
      } else {
        const template = LIST_ITEM_ACTION_TEMPLATES[action.actionType as keyof typeof LIST_ITEM_ACTION_TEMPLATES];
        if (!template) {
          throw new Error(`Unknown item action type: ${action.actionType}`);
        }
        return template;
      }
    });

    const result = await client.configureListItemActions(listUid, actionConfigs);

    return {
      content: [
        {
          type: 'text',
          text: `List item actions configured successfully:\n${JSON.stringify({
            listUid,
            actionsCount: actions.length,
            actions: actionConfigs.map((config: any) => ({
              title: config.title,
              action: config.action
            }))
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring list item actions: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理配置列表字段
 */
export async function handleConfigureListFields(client: NocoBaseClient, args: any) {
  try {
    const { listUid, fields } = args;

    const result = await client.configureListFields(listUid, fields);

    return {
      content: [
        {
          type: 'text',
          text: `List fields configured successfully:\n${JSON.stringify({
            listUid,
            fieldsCount: fields.length,
            fields: fields.map((f: any) => ({ name: f.name, title: f.title, component: f.component }))
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring list fields: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理导出列表数据
 */
export async function handleExportListData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, filter, fields, format, pageSize, page } = args;

    const result = await client.exportListData(collectionName, {
      filter,
      fields,
      format,
      pageSize,
      page
    });

    return {
      content: [
        {
          type: 'text',
          text: `List data exported successfully:\n${JSON.stringify({
            collection: collectionName,
            format,
            fieldsCount: fields?.length || 'all',
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error exporting list data: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理导入列表数据
 */
export async function handleImportListData(client: NocoBaseClient, args: any) {
  try {
    const { collectionName, file, fields, updateStrategy } = args;

    const result = await client.importListData(collectionName, file, {
      fields,
      updateStrategy
    });

    return {
      content: [
        {
          type: 'text',
          text: `List data imported successfully:\n${JSON.stringify({
            collection: collectionName,
            updateStrategy,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error importing list data: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理自定义列表请求
 */
export async function handleCustomListRequest(client: NocoBaseClient, args: any) {
  try {
    const { url, method, headers, params, data } = args;

    const result = await client.customListRequest({
      url,
      method,
      headers,
      params,
      data
    });

    return {
      content: [
        {
          type: 'text',
          text: `Custom list request completed successfully:\n${JSON.stringify({
            url,
            method,
            status: 'success',
            response: result
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error in custom list request: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 注册所有列表操作工具
 */
export async function registerListOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 获取列表数据
  server.registerTool(
    'get_list_data',
    {
      title: 'Get List Data',
      description: 'Get data for a list block with pagination, filtering, and sorting',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        page: z.number().optional().default(1).describe('Page number'),
        pageSize: z.number().optional().default(10).describe('Number of items per page'),
        filter: z.any().optional().describe('Filter conditions'),
        fields: z.array(z.string()).optional().describe('Specific fields to retrieve'),
        appends: z.array(z.string()).optional().describe('Related fields to append'),
        except: z.array(z.string()).optional().describe('Fields to exclude'),
        sort: z.array(z.string()).optional().describe('Sort fields'),
        tree: z.boolean().optional().default(false).describe('Whether to return tree structure')
      }
    },
    async (args: any) => {
      return await handleGetListData(client, args);
    }
  );

  // 创建列表项
  server.registerTool(
    'create_list_item',
    {
      title: 'Create List Item',
      description: 'Create a new item in a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        values: z.any().describe('The values for the new item'),
        filterKeys: z.array(z.string()).optional().describe('Filter keys for the operation'),
        updateAssociationValues: z.boolean().optional().default(false).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger'),
        overwriteValues: z.any().optional().describe('Values to overwrite'),
        assignedValues: z.any().optional().describe('Values to assign')
      }
    },
    async (args: any) => {
      return await handleCreateListItem(client, args);
    }
  );

  // 更新列表项
  server.registerTool(
    'update_list_item',
    {
      title: 'Update List Item',
      description: 'Update an item in a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to update'),
        values: z.any().describe('The values to update'),
        overwriteValues: z.any().optional().describe('Values to overwrite'),
        assignedValues: z.any().optional().describe('Values to assign'),
        updateAssociationValues: z.boolean().optional().default(false).describe('Whether to update association values'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger')
      }
    },
    async (args: any) => {
      return await handleUpdateListItem(client, args);
    }
  );

  // 删除列表项
  server.registerTool(
    'delete_list_item',
    {
      title: 'Delete List Item',
      description: 'Delete an item from a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to delete'),
        filter: z.any().optional().describe('Additional filter conditions'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger')
      }
    },
    async (args: any) => {
      return await handleDeleteListItem(client, args);
    }
  );

  // 批量删除列表项
  server.registerTool(
    'bulk_delete_list_items',
    {
      title: 'Bulk Delete List Items',
      description: 'Delete multiple items from a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemIds: z.array(z.union([z.string(), z.number()])).describe('Array of item IDs to delete'),
        filter: z.any().optional().describe('Additional filter conditions'),
        triggerWorkflows: z.string().optional().describe('Workflows to trigger')
      }
    },
    async (args: any) => {
      return await handleBulkDeleteListItems(client, args);
    }
  );

  // 查看列表项
  server.registerTool(
    'view_list_item',
    {
      title: 'View List Item',
      description: 'View details of a specific item in a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        itemId: z.union([z.string(), z.number()]).describe('The ID of the item to view'),
        filter: z.any().optional().describe('Additional filter conditions'),
        fields: z.array(z.string()).optional().describe('Specific fields to retrieve'),
        appends: z.array(z.string()).optional().describe('Related fields to append'),
        except: z.array(z.string()).optional().describe('Fields to exclude')
      }
    },
    async (args: any) => {
      return await handleViewListItem(client, args);
    }
  );

  // 刷新列表
  server.registerTool(
    'refresh_list',
    {
      title: 'Refresh List',
      description: 'Refresh data in a list block',
      inputSchema: {
        listUid: z.string().describe('The UID of the list block to refresh')
      }
    },
    async (args: any) => {
      return await handleRefreshList(client, args);
    }
  );

  // 筛选列表
  server.registerTool(
    'filter_list',
    {
      title: 'Filter List',
      description: 'Apply filters to a list block',
      inputSchema: {
        listUid: z.string().describe('The UID of the list block'),
        filter: z.any().describe('Filter conditions to apply'),
        page: z.number().optional().default(1).describe('Page number after filtering'),
        pageSize: z.number().optional().default(10).describe('Page size after filtering')
      }
    },
    async (args: any) => {
      return await handleFilterList(client, args);
    }
  );

  // 添加列表操作按钮
  server.registerTool(
    'add_list_action',
    {
      title: 'Add List Action',
      description: 'Add an action button to a list block',
      inputSchema: {
        listUid: z.string().describe('The UID of the list block'),
        actionType: z.enum(['create', 'filter', 'refresh', 'export', 'import', 'customRequest', 'custom']).describe('Type of action to add'),
        customConfig: z.object({
          title: z.string().describe('Action title'),
          action: z.string().describe('Action identifier'),
          icon: z.string().optional().describe('Icon name'),
          type: z.string().optional().describe('Button type'),
          align: z.enum(['left', 'right']).optional().describe('Button alignment'),
          requiresACL: z.boolean().optional().describe('Whether action requires ACL check'),
          aclAction: z.string().optional().describe('ACL action name'),
          settings: z.string().optional().describe('Settings identifier'),
          useComponentProps: z.string().optional().describe('Component props hook'),
          componentProps: z.any().optional().describe('Additional component properties')
        }).optional().describe('Custom action configuration'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the action')
      }
    },
    async (args: any) => {
      return await handleAddListAction(client, args);
    }
  );

  // 配置列表项操作按钮
  server.registerTool(
    'configure_list_item_actions',
    {
      title: 'Configure List Item Actions',
      description: 'Configure action buttons for list items',
      inputSchema: {
        listUid: z.string().describe('The UID of the list block'),
        actions: z.array(z.object({
          actionType: z.enum(['view', 'edit', 'delete', 'customRequest', 'custom']).describe('Type of action'),
          customConfig: z.object({
            title: z.string(),
            action: z.string(),
            icon: z.string().optional(),
            component: z.string().optional(),
            requiresACL: z.boolean().optional(),
            aclAction: z.string().optional(),
            settings: z.string().optional(),
            useComponentProps: z.string().optional(),
            componentProps: z.any().optional()
          }).optional().describe('Custom action configuration')
        })).describe('Array of actions to configure')
      }
    },
    async (args: any) => {
      return await handleConfigureListItemActions(client, args);
    }
  );

  // 配置列表字段
  server.registerTool(
    'configure_list_fields',
    {
      title: 'Configure List Fields',
      description: 'Configure field display in a list block',
      inputSchema: {
        listUid: z.string().describe('The UID of the list block'),
        fields: z.array(z.object({
          name: z.string().describe('Field name'),
          title: z.string().optional().describe('Field display title'),
          component: z.string().optional().describe('Display component'),
          span: z.number().optional().default(12).describe('Grid span (1-24)'),
          required: z.boolean().optional().describe('Whether field is required'),
          componentProps: z.any().optional().describe('Component properties')
        })).describe('Fields configuration')
      }
    },
    async (args: any) => {
      return await handleConfigureListFields(client, args);
    }
  );

  // 导出列表数据
  server.registerTool(
    'export_list_data',
    {
      title: 'Export List Data',
      description: 'Export data from a list block',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        filter: z.any().optional().describe('Filter conditions for export'),
        fields: z.array(z.string()).optional().describe('Fields to include in export'),
        format: z.enum(['csv', 'xlsx']).optional().default('xlsx').describe('Export format'),
        pageSize: z.number().optional().describe('Number of records per page for export'),
        page: z.number().optional().describe('Page number for export')
      }
    },
    async (args: any) => {
      return await handleExportListData(client, args);
    }
  );

  // 导入列表数据
  server.registerTool(
    'import_list_data',
    {
      title: 'Import List Data',
      description: 'Import data to a list block collection',
      inputSchema: {
        collectionName: z.string().describe('The name of the collection'),
        file: z.string().describe('File path or base64 content to import'),
        fields: z.array(z.string()).optional().describe('Field mappings for import'),
        updateStrategy: z.enum(['upsert', 'insert', 'update']).optional().default('upsert').describe('Update strategy for existing records')
      }
    },
    async (args: any) => {
      return await handleImportListData(client, args);
    }
  );

  // 自定义列表请求
  server.registerTool(
    'custom_list_request',
    {
      title: 'Custom List Request',
      description: 'Send a custom request for list block operations',
      inputSchema: {
        url: z.string().describe('Request URL'),
        method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).optional().default('GET').describe('HTTP method'),
        headers: z.any().optional().describe('Request headers'),
        params: z.any().optional().describe('URL parameters'),
        data: z.any().optional().describe('Request body data')
      }
    },
    async (args: any) => {
      return await handleCustomListRequest(client, args);
    }
  );
}

/**
 * 批量删除列表项工具
 */
export const bulkDeleteListItemsTool: Tool = {
  name: 'bulk_delete_list_items',
  description: 'Delete multiple items from a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemIds: {
        type: 'array',
        items: { type: ['string', 'number'] },
        description: 'Array of item IDs to delete'
      },
      filter: {
        type: 'object',
        description: 'Additional filter conditions'
      },
      triggerWorkflows: {
        type: 'string',
        description: 'Workflows to trigger (comma-separated)'
      }
    },
    required: ['collectionName', 'itemIds']
  }
};

/**
 * 查看列表项工具
 */
export const viewListItemTool: Tool = {
  name: 'view_list_item',
  description: 'View details of a specific item in a list block',
  inputSchema: {
    type: 'object',
    properties: {
      collectionName: {
        type: 'string',
        description: 'The name of the collection'
      },
      itemId: {
        type: ['string', 'number'],
        description: 'The ID of the item to view'
      },
      filter: {
        type: 'object',
        description: 'Additional filter conditions'
      },
      fields: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific fields to retrieve'
      },
      appends: {
        type: 'array',
        items: { type: 'string' },
        description: 'Related fields to append'
      },
      except: {
        type: 'array',
        items: { type: 'string' },
        description: 'Fields to exclude'
      }
    },
    required: ['collectionName', 'itemId']
  }
};
