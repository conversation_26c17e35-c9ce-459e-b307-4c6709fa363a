/**
 * NocoBase Menu Operations Tools
 * 提供菜单路由管理功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';

/**
 * 创建菜单路由工具
 */
export const createMenuRouteTool: Tool = {
  name: 'create_menu_route',
  description: 'Create a new menu route item',
  inputSchema: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: 'Menu title'
      },
      type: {
        type: 'string',
        enum: ['group', 'page', 'link', 'url'],
        description: 'Menu type'
      },
      icon: {
        type: 'string',
        description: 'Menu icon'
      },
      tooltip: {
        type: 'string',
        description: 'Tooltip text'
      },
      parentId: {
        type: ['number', 'null'],
        description: 'Parent menu ID'
      },
      schemaUid: {
        type: 'string',
        description: 'Page schema UID (for page type)'
      },
      options: {
        type: 'object',
        description: 'Additional options (for link/url type)',
        properties: {
          href: { type: 'string', description: 'Link URL' },
          target: { type: 'string', description: 'Link target' }
        }
      },
      sort: {
        type: 'number',
        description: 'Sort order'
      },
      hideInMenu: {
        type: 'boolean',
        description: 'Hide in menu',
        default: false
      },
      enableTabs: {
        type: 'boolean',
        description: 'Enable tabs',
        default: false
      },
      enableHeader: {
        type: 'boolean',
        description: 'Enable header',
        default: true
      }
    },
    required: ['title', 'type']
  }
};

export async function handleCreateMenuRoute(client: NocoBaseClient, args: any) {
  try {
    const {
      title,
      type,
      icon,
      tooltip,
      parentId,
      schemaUid,
      options,
      sort,
      hideInMenu = false,
      enableTabs = false,
      enableHeader = true
    } = args;

    const routeData: any = {
      title,
      type,
      icon,
      tooltip,
      parentId,
      schemaUid,
      options,
      sort,
      hideInMenu,
      enableTabs,
      enableHeader
    };

    const result = await client.createMenuRoute(routeData);

    return {
      content: [
        {
          type: 'text',
          text: `Menu route created successfully:\n${JSON.stringify({
            id: result.id,
            title,
            type,
            parentId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating menu route: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 更新菜单路由工具
 */
export const updateMenuRouteTool: Tool = {
  name: 'update_menu_route',
  description: 'Update an existing menu route item',
  inputSchema: {
    type: 'object',
    properties: {
      routeId: {
        type: ['number', 'string'],
        description: 'Menu route ID to update'
      },
      title: {
        type: 'string',
        description: 'Menu title'
      },
      icon: {
        type: 'string',
        description: 'Menu icon'
      },
      tooltip: {
        type: 'string',
        description: 'Tooltip text'
      },
      options: {
        type: 'object',
        description: 'Additional options',
        properties: {
          href: { type: 'string', description: 'Link URL' },
          target: { type: 'string', description: 'Link target' }
        }
      },
      hideInMenu: {
        type: 'boolean',
        description: 'Hide in menu'
      },
      enableTabs: {
        type: 'boolean',
        description: 'Enable tabs'
      },
      enableHeader: {
        type: 'boolean',
        description: 'Enable header'
      }
    },
    required: ['routeId']
  }
};

export async function handleUpdateMenuRoute(client: NocoBaseClient, args: any) {
  try {
    const { routeId, ...updateData } = args;

    const result = await client.updateMenuRoute(routeId, updateData);

    return {
      content: [
        {
          type: 'text',
          text: `Menu route updated successfully:\n${JSON.stringify({
            routeId,
            updateData,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating menu route: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 删除菜单路由工具
 */
export const deleteMenuRouteTool: Tool = {
  name: 'delete_menu_route',
  description: 'Delete a menu route item',
  inputSchema: {
    type: 'object',
    properties: {
      routeId: {
        type: ['number', 'string'],
        description: 'Menu route ID to delete'
      }
    },
    required: ['routeId']
  }
};

export async function handleDeleteMenuRoute(client: NocoBaseClient, args: any) {
  try {
    const { routeId } = args;

    await client.deleteMenuRoute(routeId);

    return {
      content: [
        {
          type: 'text',
          text: `Menu route deleted successfully: ${routeId}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error deleting menu route: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 移动菜单路由工具
 */
export const moveMenuRouteTool: Tool = {
  name: 'move_menu_route',
  description: 'Move a menu route to a new position',
  inputSchema: {
    type: 'object',
    properties: {
      sourceId: {
        type: ['number', 'string'],
        description: 'Source menu route ID'
      },
      targetId: {
        type: ['number', 'string'],
        description: 'Target menu route ID'
      },
      method: {
        type: 'string',
        enum: ['insertAfter', 'prepend', 'append'],
        description: 'Insert method',
        default: 'insertAfter'
      },
      sortField: {
        type: 'string',
        description: 'Sort field',
        default: 'sort'
      }
    },
    required: ['sourceId']
  }
};

export async function handleMoveMenuRoute(client: NocoBaseClient, args: any) {
  try {
    const { sourceId, targetId, method = 'insertAfter', sortField = 'sort' } = args;

    const result = await client.moveMenuRoute({
      sourceId,
      targetId,
      method,
      sortField
    });

    return {
      content: [
        {
          type: 'text',
          text: `Menu route moved successfully:\n${JSON.stringify({
            sourceId,
            targetId,
            method,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error moving menu route: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 获取可访问菜单路由工具
 */
export const getAccessibleMenuRoutesTool: Tool = {
  name: 'get_accessible_menu_routes',
  description: 'Get accessible menu routes for current user',
  inputSchema: {
    type: 'object',
    properties: {
      tree: {
        type: 'boolean',
        description: 'Return as tree structure',
        default: true
      },
      filter: {
        type: 'object',
        description: 'Filter conditions'
      },
      sort: {
        type: 'string',
        description: 'Sort conditions'
      }
    }
  }
};

export async function handleGetAccessibleMenuRoutes(client: NocoBaseClient, args: any) {
  try {
    const { tree = true, filter, sort } = args;

    const routes = await client.getAccessibleMenuRoutes({
      tree,
      filter,
      sort
    });

    return {
      content: [
        {
          type: 'text',
          text: `Accessible menu routes retrieved successfully:\n${JSON.stringify({
            routesCount: Array.isArray(routes) ? routes.length : 0,
            tree,
            routes
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving accessible menu routes: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 设置角色菜单权限工具
 */
export const setRoleMenuPermissionsTool: Tool = {
  name: 'set_role_menu_permissions',
  description: 'Set menu permissions for a role',
  inputSchema: {
    type: 'object',
    properties: {
      roleId: {
        type: ['number', 'string'],
        description: 'Role ID'
      },
      menuRouteIds: {
        type: 'array',
        items: { type: ['number', 'string'] },
        description: 'Array of menu route IDs to grant access'
      }
    },
    required: ['roleId', 'menuRouteIds']
  }
};

export async function handleSetRoleMenuPermissions(client: NocoBaseClient, args: any) {
  try {
    const { roleId, menuRouteIds } = args;

    await client.setRoleMenuPermissions(roleId, menuRouteIds);

    return {
      content: [
        {
          type: 'text',
          text: `Role menu permissions set successfully:\n${JSON.stringify({
            roleId,
            menuRouteIds,
            permissionsCount: menuRouteIds.length
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error setting role menu permissions: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建菜单分组工具
 */
export const createMenuGroupTool: Tool = {
  name: 'create_menu_group',
  description: 'Create a menu group',
  inputSchema: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: 'Group title'
      },
      icon: {
        type: 'string',
        description: 'Group icon'
      },
      parentId: {
        type: ['number', 'null'],
        description: 'Parent menu ID'
      },
      insertPosition: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Insert position relative to target',
        default: 'beforeEnd'
      },
      targetId: {
        type: ['number', 'string'],
        description: 'Target menu ID for positioning'
      }
    },
    required: ['title']
  }
};

export async function handleCreateMenuGroup(client: NocoBaseClient, args: any) {
  try {
    const { title, icon, parentId, insertPosition = 'beforeEnd', targetId } = args;

    const result = await client.createMenuGroup({
      title,
      icon,
      parentId,
      insertPosition,
      targetId
    });

    return {
      content: [
        {
          type: 'text',
          text: `Menu group created successfully:\n${JSON.stringify({
            id: result.id,
            title,
            icon,
            parentId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating menu group: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建菜单页面工具
 */
export const createMenuPageTool: Tool = {
  name: 'create_menu_page',
  description: 'Create a menu page item',
  inputSchema: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: 'Page title'
      },
      icon: {
        type: 'string',
        description: 'Page icon'
      },
      parentId: {
        type: ['number', 'null'],
        description: 'Parent menu ID'
      },
      schemaUid: {
        type: 'string',
        description: 'Page schema UID'
      },
      insertPosition: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Insert position relative to target',
        default: 'beforeEnd'
      },
      targetId: {
        type: ['number', 'string'],
        description: 'Target menu ID for positioning'
      },
      enableTabs: {
        type: 'boolean',
        description: 'Enable tabs for this page',
        default: false
      },
      enableHeader: {
        type: 'boolean',
        description: 'Enable header for this page',
        default: true
      }
    },
    required: ['title', 'schemaUid']
  }
};

export async function handleCreateMenuPage(client: NocoBaseClient, args: any) {
  try {
    const {
      title,
      icon,
      parentId,
      schemaUid,
      insertPosition = 'beforeEnd',
      targetId,
      enableTabs = false,
      enableHeader = true
    } = args;

    const result = await client.createMenuPage({
      title,
      icon,
      parentId,
      schemaUid,
      insertPosition,
      targetId,
      enableTabs,
      enableHeader
    });

    return {
      content: [
        {
          type: 'text',
          text: `Menu page created successfully:\n${JSON.stringify({
            id: result.id,
            title,
            schemaUid,
            parentId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating menu page: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 创建菜单链接工具
 */
export const createMenuLinkTool: Tool = {
  name: 'create_menu_link',
  description: 'Create a menu link item',
  inputSchema: {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        description: 'Link title'
      },
      icon: {
        type: 'string',
        description: 'Link icon'
      },
      href: {
        type: 'string',
        description: 'Link URL'
      },
      target: {
        type: 'string',
        enum: ['_self', '_blank', '_parent', '_top'],
        description: 'Link target',
        default: '_self'
      },
      parentId: {
        type: ['number', 'null'],
        description: 'Parent menu ID'
      },
      insertPosition: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Insert position relative to target',
        default: 'beforeEnd'
      },
      targetId: {
        type: ['number', 'string'],
        description: 'Target menu ID for positioning'
      }
    },
    required: ['title', 'href']
  }
};

export async function handleCreateMenuLink(client: NocoBaseClient, args: any) {
  try {
    const {
      title,
      icon,
      href,
      target = '_self',
      parentId,
      insertPosition = 'beforeEnd',
      targetId
    } = args;

    const result = await client.createMenuLink({
      title,
      icon,
      href,
      target,
      parentId,
      insertPosition,
      targetId
    });

    return {
      content: [
        {
          type: 'text',
          text: `Menu link created successfully:\n${JSON.stringify({
            id: result.id,
            title,
            href,
            target,
            parentId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating menu link: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有菜单操作工具
 */
export async function registerMenuOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 创建菜单路由工具 - 已废弃，使用 routes.ts 中的 create_menu_route 替代
  /*
  server.registerTool(
    'create_menu_route',
    {
      title: 'Create Menu Route',
      description: 'Create a new menu route item',
      inputSchema: {
        title: z.string().describe('Menu title'),
        type: z.enum(['group', 'page', 'link', 'url']).describe('Menu type'),
        icon: z.string().optional().describe('Menu icon'),
        tooltip: z.string().optional().describe('Tooltip text'),
        parentId: z.union([z.number(), z.null()]).optional().describe('Parent menu ID'),
        schemaUid: z.string().optional().describe('Page schema UID (for page type)'),
        options: z.object({
          href: z.string().optional().describe('Link URL'),
          target: z.string().optional().describe('Link target')
        }).optional().describe('Additional options (for link/url type)'),
        sort: z.number().optional().describe('Sort order'),
        hideInMenu: z.boolean().optional().default(false).describe('Hide in menu'),
        enableTabs: z.boolean().optional().default(false).describe('Enable tabs'),
        enableHeader: z.boolean().optional().default(true).describe('Enable header')
      }
    },
    async (args: any) => {
      return await handleCreateMenuRoute(client, args);
    }
  );
  */

  // 更新菜单路由工具
  server.registerTool(
    'update_menu_route',
    {
      title: 'Update Menu Route',
      description: 'Update an existing menu route item',
      inputSchema: {
        routeId: z.union([z.number(), z.string()]).describe('Menu route ID to update'),
        title: z.string().optional().describe('Menu title'),
        icon: z.string().optional().describe('Menu icon'),
        tooltip: z.string().optional().describe('Tooltip text'),
        options: z.object({
          href: z.string().optional().describe('Link URL'),
          target: z.string().optional().describe('Link target')
        }).optional().describe('Additional options'),
        hideInMenu: z.boolean().optional().describe('Hide in menu'),
        enableTabs: z.boolean().optional().describe('Enable tabs'),
        enableHeader: z.boolean().optional().describe('Enable header')
      }
    },
    async (args: any) => {
      return await handleUpdateMenuRoute(client, args);
    }
  );

  // 删除菜单路由工具
  server.registerTool(
    'delete_menu_route',
    {
      title: 'Delete Menu Route',
      description: 'Delete a menu route item',
      inputSchema: {
        routeId: z.union([z.number(), z.string()]).describe('Menu route ID to delete')
      }
    },
    async (args: any) => {
      return await handleDeleteMenuRoute(client, args);
    }
  );

  // 移动菜单路由工具
  server.registerTool(
    'move_menu_route',
    {
      title: 'Move Menu Route',
      description: 'Move a menu route to a new position',
      inputSchema: {
        sourceId: z.union([z.number(), z.string()]).describe('Source menu route ID'),
        targetId: z.union([z.number(), z.string()]).optional().describe('Target menu route ID'),
        method: z.enum(['insertAfter', 'prepend', 'append']).optional().default('insertAfter').describe('Insert method'),
        sortField: z.string().optional().default('sort').describe('Sort field')
      }
    },
    async (args: any) => {
      return await handleMoveMenuRoute(client, args);
    }
  );

  // 获取可访问菜单路由工具
  server.registerTool(
    'get_accessible_menu_routes',
    {
      title: 'Get Accessible Menu Routes',
      description: 'Get accessible menu routes for current user',
      inputSchema: {
        tree: z.boolean().optional().default(true).describe('Return as tree structure'),
        filter: z.any().optional().describe('Filter conditions'),
        sort: z.string().optional().describe('Sort conditions')
      }
    },
    async (args: any) => {
      return await handleGetAccessibleMenuRoutes(client, args);
    }
  );

  // 设置角色菜单权限工具
  server.registerTool(
    'set_role_menu_permissions',
    {
      title: 'Set Role Menu Permissions',
      description: 'Set menu permissions for a role',
      inputSchema: {
        roleId: z.union([z.number(), z.string()]).describe('Role ID'),
        menuRouteIds: z.array(z.union([z.number(), z.string()])).describe('Array of menu route IDs to grant access')
      }
    },
    async (args: any) => {
      return await handleSetRoleMenuPermissions(client, args);
    }
  );

  // 创建菜单分组工具
  server.registerTool(
    'create_menu_group',
    {
      title: 'Create Menu Group',
      description: 'Create a menu group',
      inputSchema: {
        title: z.string().describe('Group title'),
        icon: z.string().optional().describe('Group icon'),
        parentId: z.union([z.number(), z.null()]).optional().describe('Parent menu ID'),
        insertPosition: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Insert position relative to target'),
        targetId: z.union([z.number(), z.string()]).optional().describe('Target menu ID for positioning')
      }
    },
    async (args: any) => {
      return await handleCreateMenuGroup(client, args);
    }
  );

  // 创建菜单页面工具
  server.registerTool(
    'create_menu_page',
    {
      title: 'Create Menu Page',
      description: 'Create a menu page item',
      inputSchema: {
        title: z.string().describe('Page title'),
        icon: z.string().optional().describe('Page icon'),
        parentId: z.union([z.number(), z.null()]).optional().describe('Parent menu ID'),
        schemaUid: z.string().describe('Page schema UID'),
        insertPosition: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Insert position relative to target'),
        targetId: z.union([z.number(), z.string()]).optional().describe('Target menu ID for positioning'),
        enableTabs: z.boolean().optional().default(false).describe('Enable tabs for this page'),
        enableHeader: z.boolean().optional().default(true).describe('Enable header for this page')
      }
    },
    async (args: any) => {
      return await handleCreateMenuPage(client, args);
    }
  );

  // 创建菜单链接工具
  server.registerTool(
    'create_menu_link',
    {
      title: 'Create Menu Link',
      description: 'Create a menu link item',
      inputSchema: {
        title: z.string().describe('Link title'),
        icon: z.string().optional().describe('Link icon'),
        href: z.string().describe('Link URL'),
        target: z.enum(['_self', '_blank', '_parent', '_top']).optional().default('_self').describe('Link target'),
        parentId: z.union([z.number(), z.null()]).optional().describe('Parent menu ID'),
        insertPosition: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Insert position relative to target'),
        targetId: z.union([z.number(), z.string()]).optional().describe('Target menu ID for positioning')
      }
    },
    async (args: any) => {
      return await handleCreateMenuLink(client, args);
    }
  );
}
