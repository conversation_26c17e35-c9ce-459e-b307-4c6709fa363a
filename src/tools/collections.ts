import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type Field } from "../client.js";

// Collection naming validation and suggestions
function validateCollectionNaming(params: { name: string; title?: string; description?: string }) {
  const suggestions: string[] = [];
  const warnings: string[] = [];

  // Check if collection name is plural
  if (!params.name.endsWith('s') && !params.name.includes('_')) {
    suggestions.push(`Consider using plural form: '${params.name}s'`);
  }

  // Check if title uses English
  if (params.title && /[\u4e00-\u9fa5]/.test(params.title)) {
    warnings.push("Title contains Chinese characters. Consider using English for better internationalization.");
  }

  // Check if description uses English
  if (params.description && /[\u4e00-\u9fa5]/.test(params.description)) {
    warnings.push("Description contains Chinese characters. Consider using English for better internationalization.");
  }

  // Check naming conventions
  if (params.name !== params.name.toLowerCase()) {
    warnings.push("Collection name should be lowercase");
  }

  if (params.title && params.title !== params.title.charAt(0).toUpperCase() + params.title.slice(1)) {
    suggestions.push("Title should start with capital letter");
  }

  return { suggestions, warnings };
}

export async function registerCollectionTools(server: McpServer, client: NocoBaseClient) {
  // List all collections
  server.registerTool(
    "list_collections",
    {
      title: "List Collections",
      description: "List all collections in the NocoBase application",
      inputSchema: {
        includeMeta: z.boolean().optional().describe("Include detailed metadata for each collection")
      }
    },
    async ({ includeMeta = false }) => {
      try {
        const collections = includeMeta 
          ? await client.listCollectionsMeta()
          : await client.listCollections();

        return {
          content: [{
            type: "text",
            text: `Found ${collections.length} collections:\n\n${collections.map(c => 
              `• ${c.name} (${c.title || 'No title'})\n  ${c.description || 'No description'}\n  Fields: ${c.fields?.length || 'Unknown'}`
            ).join('\n\n')}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing collections: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get collection details
  server.registerTool(
    "get_collection",
    {
      title: "Get Collection Details",
      description: "Get detailed information about a specific collection",
      inputSchema: {
        name: z.string().describe("Name of the collection to retrieve")
      }
    },
    async ({ name }) => {
      try {
        const collection = await client.getCollection(name);

        const details = [
          `Collection: ${collection.name}`,
          `Title: ${collection.title || 'No title'}`,
          `Description: ${collection.description || 'No description'}`,
          `Auto-generated ID: ${collection.autoGenId ? 'Yes' : 'No'}`,
          `Timestamps: ${collection.createdAt ? 'Created' : ''}${collection.updatedAt ? (collection.createdAt ? ', Updated' : 'Updated') : ''}`,
          `User tracking: ${collection.createdBy ? 'Created by' : ''}${collection.updatedBy ? (collection.createdBy ? ', Updated by' : 'Updated by') : ''}`,
          `Hidden: ${collection.hidden ? 'Yes' : 'No'}`,
          `Inherits: ${collection.inherit ? 'Yes' : 'No'}`,
          '',
          `Fields (${collection.fields?.length || 0}):`
        ];

        if (collection.fields && collection.fields.length > 0) {
          collection.fields.forEach(field => {
            details.push(`  • ${field.name} (${field.type}) - ${field.interface || 'No interface'}`);
            if (field.description) {
              details.push(`    ${field.description}`);
            }
          });
        } else {
          details.push('  No fields found');
        }

        return {
          content: [{
            type: "text",
            text: details.join('\n')
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create collection
  server.registerTool(
    "create_collection",
    {
      title: "Create Collection",
      description: "Create a new collection in NocoBase with naming best practices",
      inputSchema: {
        name: z.string()
          .regex(/^[a-z][a-zA-Z0-9_]*$/, "Collection name must start with lowercase letter, contain only letters, numbers, and underscores")
          .min(3, "Collection name must be at least 3 characters")
          .max(50, "Collection name must be at most 50 characters")
          .describe("Name of the collection (preferably plural noun, e.g., 'users', 'towns', 'products')"),
        title: z.string()
          .min(1, "Title is required")
          .max(100, "Title must be at most 100 characters")
          .describe("Display title in English (e.g., 'Users', 'Towns', 'Products')"),
        description: z.string()
          .min(10, "Description should be at least 10 characters")
          .max(500, "Description must be at most 500 characters")
          .describe("English description of the collection's purpose and functionality"),
        category: z.array(z.string()).optional()
          .describe("Collection categories in English (e.g., ['Administrative', 'Geography', 'User Management'])"),
        autoGenId: z.boolean().optional().default(true).describe("Auto-generate ID field"),
        createdAt: z.boolean().optional().default(true).describe("Add created timestamp field"),
        updatedAt: z.boolean().optional().default(true).describe("Add updated timestamp field"),
        createdBy: z.boolean().optional().default(false).describe("Add created by user field"),
        updatedBy: z.boolean().optional().default(false).describe("Add updated by user field"),
        fields: z.array(z.object({
          name: z.string(),
          type: z.string(),
          interface: z.string().optional(),
          uiSchema: z.any().optional()
        })).optional().describe("Initial fields to create")
      }
    },
    async (params) => {
      try {
        // Validate naming conventions and provide suggestions
        const namingValidation = validateCollectionNaming(params);
        let responseMessage = '';

        if (namingValidation.warnings.length > 0) {
          responseMessage += '\n⚠️  Naming Warnings:\n' + namingValidation.warnings.map(w => `  • ${w}`).join('\n');
        }

        if (namingValidation.suggestions.length > 0) {
          responseMessage += '\n💡 Naming Suggestions:\n' + namingValidation.suggestions.map(s => `  • ${s}`).join('\n');
        }

        // 准备默认字段定义
        const defaultFields: any[] = [];

        // ID 字段
        if (params.autoGenId !== false) {
          defaultFields.push({
            name: 'id',
            type: 'bigInt',
            interface: 'id',
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
            uiSchema: {
              type: 'number',
              title: '{{t("ID")}}',
              'x-component': 'InputNumber',
              'x-read-pretty': true
            }
          });
        }

        // 创建时间字段
        if (params.createdAt !== false) {
          defaultFields.push({
            name: 'createdAt',
            type: 'date',
            interface: 'createdAt',
            field: 'createdAt',
            uiSchema: {
              type: 'datetime',
              title: '{{t("Created at")}}',
              'x-component': 'DatePicker',
              'x-component-props': {},
              'x-read-pretty': true
            }
          });
        }

        // 更新时间字段
        if (params.updatedAt !== false) {
          defaultFields.push({
            name: 'updatedAt',
            type: 'date',
            interface: 'updatedAt',
            field: 'updatedAt',
            uiSchema: {
              type: 'datetime',
              title: '{{t("Last updated at")}}',
              'x-component': 'DatePicker',
              'x-component-props': {},
              'x-read-pretty': true
            }
          });
        }

        // 创建人字段
        if (params.createdBy === true) {
          defaultFields.push({
            name: 'createdBy',
            type: 'belongsTo',
            interface: 'createdBy',
            target: 'users',
            foreignKey: 'createdById',
            targetKey: 'id',
            uiSchema: {
              type: 'object',
              title: '{{t("Created by")}}',
              'x-component': 'AssociationField',
              'x-component-props': {
                fieldNames: {
                  value: 'id',
                  label: 'nickname'
                }
              },
              'x-read-pretty': true
            }
          });
        }

        // 更新人字段
        if (params.updatedBy === true) {
          defaultFields.push({
            name: 'updatedBy',
            type: 'belongsTo',
            interface: 'updatedBy',
            target: 'users',
            foreignKey: 'updatedById',
            targetKey: 'id',
            uiSchema: {
              type: 'object',
              title: '{{t("Last updated by")}}',
              'x-component': 'AssociationField',
              'x-component-props': {
                fieldNames: {
                  value: 'id',
                  label: 'nickname'
                }
              },
              'x-read-pretty': true
            }
          });
        }

        // 合并用户提供的字段和默认字段
        const allFields = [...defaultFields, ...(params.fields || [])];

        // 准备集合参数
        const collectionParams = {
          ...params,
          fields: allFields
        };

        // Filter out undefined values
        const cleanParams = Object.fromEntries(
          Object.entries(collectionParams).filter(([_, value]) => value !== undefined)
        );

        const collection = await client.createCollection(cleanParams);

        const defaultFieldsCount = defaultFields.length;
        const userFieldsCount = (params.fields || []).length;

        return {
          content: [{
            type: "text",
            text: `✅ Successfully created collection '${collection.name}' (${collection.title || 'No title'}) with ${defaultFieldsCount} default fields and ${userFieldsCount} custom fields${responseMessage}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating collection '${params.name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update collection
  server.registerTool(
    "update_collection",
    {
      title: "Update Collection",
      description: "Update an existing collection's configuration",
      inputSchema: {
        name: z.string().describe("Name of the collection to update"),
        title: z.string().optional().describe("New display title"),
        description: z.string().optional().describe("New description"),
        hidden: z.boolean().optional().describe("Hide/show the collection")
      }
    },
    async ({ name, ...updates }) => {
      try {
        // Filter out undefined values
        const cleanUpdates = Object.fromEntries(
          Object.entries(updates).filter(([_, value]) => value !== undefined)
        );
        const collection = await client.updateCollection(name, cleanUpdates);

        return {
          content: [{
            type: "text",
            text: `Successfully updated collection '${collection.name}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete collection
  server.registerTool(
    "delete_collection",
    {
      title: "Delete Collection",
      description: "Delete a collection and all its data (use with caution!)",
      inputSchema: {
        name: z.string().describe("Name of the collection to delete"),
        confirm: z.boolean().describe("Confirmation that you want to delete the collection and all its data")
      }
    },
    async ({ name, confirm }) => {
      if (!confirm) {
        return {
          content: [{
            type: "text",
            text: "Collection deletion cancelled. Set 'confirm' to true to proceed with deletion."
          }]
        };
      }

      try {
        await client.deleteCollection(name);

        return {
          content: [{
            type: "text",
            text: `Successfully deleted collection '${name}' and all its data`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting collection '${name}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Move collection
  server.registerTool(
    "move_collection",
    {
      title: "Move Collection",
      description: "Move a collection to a different position in the sort order",
      inputSchema: {
        sourceId: z.string().describe("ID of the collection to move"),
        targetId: z.string().optional().describe("ID of the target collection to move relative to"),
        targetScope: z.any().optional().describe("Target scope for the collection"),
        sortField: z.string().optional().describe("Field to use for sorting"),
        sticky: z.boolean().optional().describe("Whether to make the collection sticky"),
        method: z.enum(['insertAfter', 'insertBefore']).optional().default('insertAfter').describe("Insert method relative to target")
      }
    },
    async ({ sourceId, targetId, targetScope, sortField, sticky, method }) => {
      try {
        const moveOptions: any = { sourceId };
        if (targetId !== undefined) moveOptions.targetId = targetId;
        if (targetScope !== undefined) moveOptions.targetScope = targetScope;
        if (sortField !== undefined) moveOptions.sortField = sortField;
        if (sticky !== undefined) moveOptions.sticky = sticky;
        if (method !== undefined) moveOptions.method = method;

        await client.moveCollection(moveOptions);

        return {
          content: [{
            type: "text",
            text: `Successfully moved collection '${sourceId}'${targetId ? ` relative to '${targetId}'` : ''}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error moving collection '${sourceId}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Set collection fields
  server.registerTool(
    "set_collection_fields",
    {
      title: "Set Collection Fields",
      description: "Set/replace all fields for a collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        fields: z.array(z.object({
          name: z.string().describe("Field name"),
          type: z.string().describe("Field type"),
          interface: z.string().optional().describe("Field interface"),
          description: z.string().optional().describe("Field description"),
          uiSchema: z.any().optional().describe("UI schema configuration")
        })).describe("Array of field definitions")
      }
    },
    async ({ collectionName, fields }) => {
      try {
        await client.setCollectionFields(collectionName, fields as Partial<Field>[]);

        return {
          content: [{
            type: "text",
            text: `Successfully set ${fields.length} fields for collection '${collectionName}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error setting fields for collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get field
  server.registerTool(
    "get_field",
    {
      title: "Get Field",
      description: "Get detailed information about a specific field in a collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        fieldName: z.string().describe("Name of the field")
      }
    },
    async ({ collectionName, fieldName }) => {
      try {
        const field = await client.getField(collectionName, fieldName);

        const details = [
          `Field: ${field.name}`,
          `Type: ${field.type}`,
          `Interface: ${field.interface || 'No interface'}`,
          `Collection: ${field.collectionName}`,
          `Description: ${field.description || 'No description'}`,
          `Key: ${field.key}`,
          ''
        ];

        if (field.uiSchema) {
          details.push('UI Schema:');
          details.push(JSON.stringify(field.uiSchema, null, 2));
        }

        return {
          content: [{
            type: "text",
            text: details.join('\n')
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting field '${fieldName}' from collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // List fields (already implemented in client, adding MCP tool)
  server.registerTool(
    "list_fields",
    {
      title: "List Fields",
      description: "List all fields in a collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection")
      }
    },
    async ({ collectionName }) => {
      try {
        const fields = await client.listFields(collectionName);

        return {
          content: [{
            type: "text",
            text: `Found ${fields.length} fields in collection '${collectionName}':\n\n${fields.map(f =>
              `• ${f.name} (${f.type}) - ${f.interface || 'No interface'}\n  ${f.description || 'No description'}`
            ).join('\n\n')}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing fields for collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create field (already implemented in client, adding MCP tool)
  server.registerTool(
    "create_field",
    {
      title: "Create Field",
      description: "Create a new field in a collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        name: z.string().describe("Name of the field"),
        type: z.string().describe("Type of the field (e.g., 'string', 'integer', 'boolean', 'text', 'date')"),
        interface: z.string().optional().describe("Interface type for the field"),
        description: z.string().optional().describe("Description of the field"),
        uiSchema: z.any().optional().describe("UI schema configuration for the field"),
        required: z.boolean().optional().describe("Whether the field is required"),
        unique: z.boolean().optional().describe("Whether the field must be unique"),
        defaultValue: z.any().optional().describe("Default value for the field")
      }
    },
    async ({ collectionName, name, type, interface: fieldInterface, description, uiSchema, required, unique, defaultValue }) => {
      try {
        const fieldData: any = {
          name,
          type
        };

        if (fieldInterface !== undefined) fieldData.interface = fieldInterface;
        if (description !== undefined) fieldData.description = description;
        if (uiSchema !== undefined) fieldData.uiSchema = uiSchema;
        if (required !== undefined) fieldData.required = required;
        if (unique !== undefined) fieldData.unique = unique;
        if (defaultValue !== undefined) fieldData.defaultValue = defaultValue;

        const field = await client.createField(collectionName, fieldData);

        return {
          content: [{
            type: "text",
            text: `Successfully created field '${field.name}' in collection '${collectionName}'\nType: ${field.type}\nInterface: ${field.interface || 'None'}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating field '${name}' in collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update field
  server.registerTool(
    "update_field",
    {
      title: "Update Field",
      description: "Update an existing field in a collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        fieldName: z.string().describe("Name of the field to update"),
        type: z.string().optional().describe("New type of the field"),
        interface: z.string().optional().describe("New interface type for the field"),
        description: z.string().optional().describe("New description of the field"),
        uiSchema: z.any().optional().describe("New UI schema configuration for the field"),
        required: z.boolean().optional().describe("Whether the field is required"),
        unique: z.boolean().optional().describe("Whether the field must be unique"),
        defaultValue: z.any().optional().describe("New default value for the field")
      }
    },
    async ({ collectionName, fieldName, type, interface: fieldInterface, description, uiSchema, required, unique, defaultValue }) => {
      try {
        const updates: Partial<Field> = {};
        if (type !== undefined) updates.type = type;
        if (fieldInterface !== undefined) updates.interface = fieldInterface;
        if (description !== undefined) updates.description = description;
        if (uiSchema !== undefined) updates.uiSchema = uiSchema;
        if (required !== undefined) updates.required = required;
        if (unique !== undefined) updates.unique = unique;
        if (defaultValue !== undefined) updates.defaultValue = defaultValue;

        const field = await client.updateField(collectionName, fieldName, updates);

        return {
          content: [{
            type: "text",
            text: `Successfully updated field '${field.name}' in collection '${collectionName}'\nType: ${field.type}\nInterface: ${field.interface || 'None'}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating field '${fieldName}' in collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete field
  server.registerTool(
    "delete_field",
    {
      title: "Delete Field",
      description: "Delete a field from a collection (use with caution!)",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        fieldName: z.string().describe("Name of the field to delete"),
        confirm: z.boolean().describe("Confirmation that you want to delete the field")
      }
    },
    async ({ collectionName, fieldName, confirm }) => {
      if (!confirm) {
        return {
          content: [{
            type: "text",
            text: "Field deletion cancelled. Set 'confirm' to true to proceed with deletion."
          }]
        };
      }

      try {
        await client.deleteField(collectionName, fieldName);

        return {
          content: [{
            type: "text",
            text: `Successfully deleted field '${fieldName}' from collection '${collectionName}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting field '${fieldName}' from collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Move field
  server.registerTool(
    "move_field",
    {
      title: "Move Field",
      description: "Move a field to a different position in the collection",
      inputSchema: {
        collectionName: z.string().describe("Name of the collection"),
        sourceId: z.string().describe("ID of the field to move"),
        targetId: z.string().optional().describe("ID of the target field to move relative to"),
        targetScope: z.any().optional().describe("Target scope for the field"),
        sortField: z.string().optional().describe("Field to use for sorting"),
        sticky: z.boolean().optional().describe("Whether to make the field sticky"),
        method: z.enum(['insertAfter', 'insertBefore']).optional().default('insertAfter').describe("Insert method relative to target")
      }
    },
    async ({ collectionName, sourceId, targetId, targetScope, sortField, sticky, method }) => {
      try {
        const moveOptions: any = { sourceId };
        if (targetId !== undefined) moveOptions.targetId = targetId;
        if (targetScope !== undefined) moveOptions.targetScope = targetScope;
        if (sortField !== undefined) moveOptions.sortField = sortField;
        if (sticky !== undefined) moveOptions.sticky = sticky;
        if (method !== undefined) moveOptions.method = method;

        await client.moveField(collectionName, moveOptions);

        return {
          content: [{
            type: "text",
            text: `Successfully moved field '${sourceId}' in collection '${collectionName}'${targetId ? ` relative to '${targetId}'` : ''}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error moving field '${sourceId}' in collection '${collectionName}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
