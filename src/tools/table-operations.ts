/**
 * NocoBase Table Operations Tools
 * 提供表格操作按钮、列、筛选器等管理功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';
import { uid } from '../utils.js';

/**
 * 预定义的表格操作配置
 */
export const TABLE_ACTION_TEMPLATES = {
  // 基础操作
  create: {
    title: "{{t('Add new')}}",
    action: 'create',
    icon: 'PlusOutlined',
    type: 'primary',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'create',
    settings: 'actionSettings:addNew'
  },
  
  filter: {
    title: "{{t('Filter')}}",
    action: 'filter',
    icon: 'FilterOutlined',
    align: 'left' as const,
    requiresACL: false,
    settings: 'actionSettings:filter'
  },
  
  bulkDelete: {
    title: "{{t('Delete selected')}}",
    action: 'destroy',
    icon: 'DeleteOutlined',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'destroyMany',
    settings: 'actionSettings:delete'
  },
  
  export: {
    title: "{{t('Export')}}",
    action: 'export',
    icon: 'ExportOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:export'
  },
  
  import: {
    title: "{{t('Import')}}",
    action: 'import',
    icon: 'ImportOutlined',
    align: 'right' as const,
    requiresACL: true,
    aclAction: 'importXlsx',
    settings: 'actionSettings:import'
  },
  
  refresh: {
    title: "{{t('Refresh')}}",
    action: 'refresh',
    icon: 'ReloadOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:refresh'
  },
  
  // 自定义请求操作
  customRequest: {
    title: "{{t('Custom Request')}}",
    action: 'customize:table:request:global',
    icon: 'ApiOutlined',
    align: 'right' as const,
    requiresACL: false,
    settings: 'actionSettings:customRequest'
  }
};

/**
 * 添加表格操作按钮工具
 */
export const addTableActionTool: Tool = {
  name: 'add_table_action',
  description: 'Add an action button to a table block',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block to add action to'
      },
      actionType: {
        type: 'string',
        enum: ['create', 'filter', 'bulkDelete', 'export', 'import', 'refresh', 'customRequest', 'custom'],
        description: 'Type of action to add (use "custom" for custom configuration)'
      },
      customConfig: {
        type: 'object',
        description: 'Custom action configuration (required when actionType is "custom")',
        properties: {
          title: { type: 'string', description: 'Action title' },
          action: { type: 'string', description: 'Action identifier' },
          icon: { type: 'string', description: 'Icon name' },
          type: { type: 'string', description: 'Button type (primary, default, etc.)' },
          align: { type: 'string', enum: ['left', 'right'], description: 'Button alignment' },
          requiresACL: { type: 'boolean', description: 'Whether action requires ACL check' },
          aclAction: { type: 'string', description: 'ACL action name' },
          settings: { type: 'string', description: 'Settings identifier' },
          componentProps: { type: 'object', description: 'Additional component properties' }
        }
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the action'
      }
    },
    required: ['tableUid', 'actionType']
  }
};

/**
 * 删除表格操作按钮工具
 */
export const removeTableActionTool: Tool = {
  name: 'remove_table_action',
  description: 'Remove an action button from a table block',
  inputSchema: {
    type: 'object',
    properties: {
      actionUid: {
        type: 'string',
        description: 'The UID of the action to remove'
      }
    },
    required: ['actionUid']
  }
};

/**
 * 更新表格操作按钮工具
 */
export const updateTableActionTool: Tool = {
  name: 'update_table_action',
  description: 'Update an existing table action button',
  inputSchema: {
    type: 'object',
    properties: {
      actionUid: {
        type: 'string',
        description: 'The UID of the action to update'
      },
      updates: {
        type: 'object',
        description: 'Updates to apply to the action',
        properties: {
          title: { type: 'string', description: 'New action title' },
          'x-component-props': { type: 'object', description: 'New component properties' },
          'x-acl-action': { type: 'string', description: 'New ACL action' },
          'x-settings': { type: 'string', description: 'New settings identifier' }
        }
      }
    },
    required: ['actionUid', 'updates']
  }
};

/**
 * 列出表格操作按钮工具
 */
export const listTableActionsTool: Tool = {
  name: 'list_table_actions',
  description: 'List all action buttons in a table block',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block'
      }
    },
    required: ['tableUid']
  }
};

/**
 * 配置表格列工具
 */
export const configureTableColumnTool: Tool = {
  name: 'configure_table_column',
  description: 'Configure a column in a table block',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block'
      },
      fieldName: {
        type: 'string',
        description: 'Name of the field to configure'
      },
      columnConfig: {
        type: 'object',
        description: 'Column configuration',
        properties: {
          title: { type: 'string', description: 'Column title' },
          width: { type: 'number', description: 'Column width' },
          fixed: { type: 'string', enum: ['left', 'right'], description: 'Fixed position' },
          sortable: { type: 'boolean', description: 'Whether column is sortable' },
          filterable: { type: 'boolean', description: 'Whether column is filterable' },
          component: { type: 'string', description: 'Display component' },
          componentProps: { type: 'object', description: 'Component properties' }
        }
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the column'
      }
    },
    required: ['tableUid', 'fieldName', 'columnConfig']
  }
};

/**
 * 配置表格筛选器工具
 */
export const configureTableFilterTool: Tool = {
  name: 'configure_table_filter',
  description: 'Configure filters for a table block',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block'
      },
      filterConfig: {
        type: 'object',
        description: 'Filter configuration',
        properties: {
          defaultFilter: { type: 'object', description: 'Default filter conditions' },
          filterForm: { type: 'object', description: 'Filter form schema' },
          enableQuickFilter: { type: 'boolean', description: 'Enable quick filter' },
          quickFilterFields: { type: 'array', items: { type: 'string' }, description: 'Quick filter fields' }
        }
      }
    },
    required: ['tableUid', 'filterConfig']
  }
};

/**
 * 配置表格排序工具
 */
export const configureTableSortTool: Tool = {
  name: 'configure_table_sort',
  description: 'Configure sorting for a table block',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block'
      },
      sortConfig: {
        type: 'object',
        description: 'Sort configuration',
        properties: {
          defaultSort: { type: 'array', items: { type: 'string' }, description: 'Default sort fields' },
          enableDragSort: { type: 'boolean', description: 'Enable drag sorting' },
          sortField: { type: 'string', description: 'Sort field name' }
        }
      }
    },
    required: ['tableUid', 'sortConfig']
  }
};

/**
 * 发送自定义请求工具
 */
export const sendCustomRequestTool: Tool = {
  name: 'send_custom_request',
  description: 'Send a custom request from table context',
  inputSchema: {
    type: 'object',
    properties: {
      requestId: {
        type: 'string',
        description: 'The ID of the custom request to send'
      },
      requestData: {
        type: 'object',
        description: 'Data to send with the request',
        properties: {
          currentRecord: { type: 'object', description: 'Current record data' },
          selectedRecords: { type: 'array', description: 'Selected records data' },
          formData: { type: 'object', description: 'Form data if applicable' }
        }
      }
    },
    required: ['requestId']
  }
};

// Handler functions

/**
 * 处理添加表格操作按钮
 */
export async function handleAddTableAction(client: NocoBaseClient, args: any) {
  try {
    const { tableUid, actionType, customConfig, position } = args;

    let actionConfig;

    if (actionType === 'custom') {
      if (!customConfig) {
        throw new Error('customConfig is required when actionType is "custom"');
      }
      actionConfig = customConfig;
    } else {
      actionConfig = TABLE_ACTION_TEMPLATES[actionType as keyof typeof TABLE_ACTION_TEMPLATES];
      if (!actionConfig) {
        throw new Error(`Unknown action type: ${actionType}`);
      }
    }

    // 添加位置参数
    const configWithPosition = {
      ...actionConfig,
      position: position || 'beforeEnd'
    };

    const result = await client.addTableAction(tableUid, configWithPosition);

    return {
      content: [
        {
          type: 'text',
          text: `Table action "${actionConfig.title}" added successfully:\n${JSON.stringify({
            actionType,
            title: actionConfig.title,
            action: actionConfig.action,
            position: configWithPosition.position
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding table action: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理删除表格操作按钮
 */
export async function handleRemoveTableAction(client: NocoBaseClient, args: any) {
  try {
    const { actionUid } = args;

    await client.removeTableAction(actionUid);

    return {
      content: [
        {
          type: 'text',
          text: `Table action removed successfully: ${actionUid}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error removing table action: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理更新表格操作按钮
 */
export async function handleUpdateTableAction(client: NocoBaseClient, args: any) {
  try {
    const { actionUid, updates } = args;

    const result = await client.updateTableAction(actionUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Table action updated successfully:\n${JSON.stringify({
            actionUid,
            updates
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating table action: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理列出表格操作按钮
 */
export async function handleListTableActions(client: NocoBaseClient, args: any) {
  try {
    const { tableUid } = args;

    const actions = await client.getTableActions(tableUid);

    // 解析操作按钮
    const actionList: any[] = [];

    function extractActions(obj: any, path: string = '') {
      if (obj && typeof obj === 'object') {
        if (obj['x-component'] === 'Action' && obj['x-action']) {
          actionList.push({
            uid: obj['x-uid'],
            title: obj.title,
            action: obj['x-action'],
            component: obj['x-component'],
            icon: obj['x-component-props']?.icon,
            type: obj['x-component-props']?.type,
            align: obj['x-align'],
            aclAction: obj['x-acl-action'],
            settings: obj['x-settings'],
            path
          });
        }

        // 递归查找子节点
        if (obj.properties) {
          Object.keys(obj.properties).forEach(key => {
            extractActions(obj.properties[key], path ? `${path}.${key}` : key);
          });
        }
      }
    }

    extractActions(actions);

    return {
      content: [
        {
          type: 'text',
          text: `Table actions for ${tableUid}:\n${JSON.stringify(actionList, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error listing table actions: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理配置表格列
 */
export async function handleConfigureTableColumn(client: NocoBaseClient, args: any) {
  try {
    const { tableUid, fieldName, columnConfig, position } = args;

    // 创建列的 Schema
    const columnSchema = {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      'x-component-props': {
        ...columnConfig.componentProps
      },
      properties: {
        [fieldName]: {
          type: 'string',
          'x-component': columnConfig.component || 'CollectionField',
          'x-component-props': {
            ...columnConfig.componentProps
          },
          'x-read-pretty': true,
          title: columnConfig.title || fieldName
        }
      },
      title: columnConfig.title || fieldName,
      ...(columnConfig.width && { width: columnConfig.width }),
      ...(columnConfig.fixed && { fixed: columnConfig.fixed }),
      ...(columnConfig.sortable !== undefined && { sortable: columnConfig.sortable }),
      ...(columnConfig.filterable !== undefined && { filterable: columnConfig.filterable })
    };

    const result = await client.insertAdjacentSchema(
      tableUid,
      columnSchema,
      position || 'beforeEnd'
    );

    return {
      content: [
        {
          type: 'text',
          text: `Table column configured successfully:\n${JSON.stringify({
            fieldName,
            title: columnConfig.title || fieldName,
            component: columnConfig.component || 'CollectionField',
            position: position || 'beforeEnd'
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring table column: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理配置表格筛选器
 */
export async function handleConfigureTableFilter(client: NocoBaseClient, args: any) {
  try {
    const { tableUid, filterConfig } = args;

    // 更新表格的筛选配置
    const updates = {
      'x-decorator-props': {
        params: {
          ...(filterConfig.defaultFilter && { filter: filterConfig.defaultFilter }),
        }
      },
      ...(filterConfig.filterForm && { 'x-filter-form': filterConfig.filterForm }),
      ...(filterConfig.enableQuickFilter !== undefined && { 'x-enable-quick-filter': filterConfig.enableQuickFilter }),
      ...(filterConfig.quickFilterFields && { 'x-quick-filter-fields': filterConfig.quickFilterFields })
    };

    const result = await client.patchSchema(tableUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Table filter configured successfully:\n${JSON.stringify({
            tableUid,
            filterConfig
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring table filter: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理配置表格排序
 */
export async function handleConfigureTableSort(client: NocoBaseClient, args: any) {
  try {
    const { tableUid, sortConfig } = args;

    // 更新表格的排序配置
    const updates = {
      'x-decorator-props': {
        params: {
          ...(sortConfig.defaultSort && { sort: sortConfig.defaultSort }),
        },
        ...(sortConfig.enableDragSort !== undefined && { dragSort: sortConfig.enableDragSort }),
        ...(sortConfig.sortField && { sortField: sortConfig.sortField })
      }
    };

    const result = await client.patchSchema(tableUid, updates);

    return {
      content: [
        {
          type: 'text',
          text: `Table sort configured successfully:\n${JSON.stringify({
            tableUid,
            sortConfig
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error configuring table sort: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理发送自定义请求
 */
export async function handleSendCustomRequest(client: NocoBaseClient, args: any) {
  try {
    const { requestId, requestData = {} } = args;

    const result = await client.sendCustomRequest(requestId, requestData);

    return {
      content: [
        {
          type: 'text',
          text: `Custom request sent successfully:\n${JSON.stringify({
            requestId,
            result
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error sending custom request: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 注册所有表格操作工具
 */
export async function registerTableOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 添加表格操作按钮
  server.registerTool(
    'add_table_action',
    {
      title: 'Add Table Action',
      description: 'Add an action button to a table block',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block to add action to'),
        actionType: z.enum(['create', 'filter', 'bulkDelete', 'export', 'import', 'refresh', 'customRequest', 'custom']).describe('Type of action to add'),
        customConfig: z.object({
          title: z.string().describe('Action title'),
          action: z.string().describe('Action identifier'),
          icon: z.string().optional().describe('Icon name'),
          type: z.string().optional().describe('Button type'),
          align: z.enum(['left', 'right']).optional().describe('Button alignment'),
          requiresACL: z.boolean().optional().describe('Whether action requires ACL check'),
          aclAction: z.string().optional().describe('ACL action name'),
          settings: z.string().optional().describe('Settings identifier'),
          componentProps: z.any().optional().describe('Additional component properties')
        }).optional().describe('Custom action configuration (required when actionType is "custom")'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().describe('Position to insert the action')
      }
    },
    async (args: any) => {
      return await handleAddTableAction(client, args);
    }
  );

  // 删除表格操作按钮
  server.registerTool(
    'remove_table_action',
    {
      title: 'Remove Table Action',
      description: 'Remove an action button from a table block',
      inputSchema: {
        actionUid: z.string().describe('The UID of the action to remove')
      }
    },
    async (args: any) => {
      return await handleRemoveTableAction(client, args);
    }
  );

  // 更新表格操作按钮
  server.registerTool(
    'update_table_action',
    {
      title: 'Update Table Action',
      description: 'Update an existing table action button',
      inputSchema: {
        actionUid: z.string().describe('The UID of the action to update'),
        updates: z.object({
          title: z.string().optional().describe('New action title'),
          'x-component-props': z.any().optional().describe('New component properties'),
          'x-acl-action': z.string().optional().describe('New ACL action'),
          'x-settings': z.string().optional().describe('New settings identifier')
        }).describe('Updates to apply to the action')
      }
    },
    async (args: any) => {
      return await handleUpdateTableAction(client, args);
    }
  );

  // 列出表格操作按钮
  server.registerTool(
    'list_table_actions',
    {
      title: 'List Table Actions',
      description: 'List all action buttons in a table block',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block')
      }
    },
    async (args: any) => {
      return await handleListTableActions(client, args);
    }
  );

  // 配置表格列
  server.registerTool(
    'configure_table_column',
    {
      title: 'Configure Table Column',
      description: 'Configure a column in a table block',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block'),
        fieldName: z.string().describe('Name of the field to configure'),
        columnConfig: z.object({
          title: z.string().optional().describe('Column title'),
          width: z.number().optional().describe('Column width'),
          fixed: z.enum(['left', 'right']).optional().describe('Fixed position'),
          sortable: z.boolean().optional().describe('Whether column is sortable'),
          filterable: z.boolean().optional().describe('Whether column is filterable'),
          component: z.string().optional().describe('Display component'),
          componentProps: z.any().optional().describe('Component properties')
        }).describe('Column configuration'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().describe('Position to insert the column')
      }
    },
    async (args: any) => {
      return await handleConfigureTableColumn(client, args);
    }
  );

  // 配置表格筛选器
  server.registerTool(
    'configure_table_filter',
    {
      title: 'Configure Table Filter',
      description: 'Configure filters for a table block',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block'),
        filterConfig: z.object({
          defaultFilter: z.any().optional().describe('Default filter conditions'),
          filterForm: z.any().optional().describe('Filter form schema'),
          enableQuickFilter: z.boolean().optional().describe('Enable quick filter'),
          quickFilterFields: z.array(z.string()).optional().describe('Quick filter fields')
        }).describe('Filter configuration')
      }
    },
    async (args: any) => {
      return await handleConfigureTableFilter(client, args);
    }
  );

  // 配置表格排序
  server.registerTool(
    'configure_table_sort',
    {
      title: 'Configure Table Sort',
      description: 'Configure sorting for a table block',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block'),
        sortConfig: z.object({
          defaultSort: z.array(z.string()).optional().describe('Default sort fields'),
          enableDragSort: z.boolean().optional().describe('Enable drag sorting'),
          sortField: z.string().optional().describe('Sort field name')
        }).describe('Sort configuration')
      }
    },
    async (args: any) => {
      return await handleConfigureTableSort(client, args);
    }
  );

  // 发送自定义请求
  server.registerTool(
    'send_custom_request',
    {
      title: 'Send Custom Request',
      description: 'Send a custom request from table context',
      inputSchema: {
        requestId: z.string().describe('The ID of the custom request to send'),
        requestData: z.object({
          currentRecord: z.any().optional().describe('Current record data'),
          selectedRecords: z.array(z.any()).optional().describe('Selected records data'),
          formData: z.any().optional().describe('Form data if applicable')
        }).optional().describe('Data to send with the request')
      }
    },
    async (args: any) => {
      return await handleSendCustomRequest(client, args);
    }
  );
}
